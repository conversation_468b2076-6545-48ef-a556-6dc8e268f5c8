/*!
* UX4G Accessibility beta v1.15.0 (https://doc.ux4g.gov.in/)
* Copyright 2025 The UX4G Authors(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)
* Copyright 2025 NeGD, MeitY.
* Licensed under MIT. 
*/
@font-face {
  font-family: 'Open-Dyslexic';
  font-style: normal;
  font-weight: 400;
  src: url('https://cdn.ux4g.gov.in/fonts/open-dyslexic.woff') format('woff');
  }
  

  :root {
    --color-black: #000;
    --color-black3: #161519;
    --color-black4: #212121;
    --color-white: #fff;
    --color-dark-blue-1: #613AF5;
  }
  
  .relative {
    position: relative;
  }
  
  /* icon set */
  .ux4g-icon {
    background-color: var(--color-black);
    display: inline-block;
    width: 40px;
    height: 40px;
    mask-size: contain;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    mask-position: center;
  }
  
  .icon-speaker {
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32' fill='none'%3E%3Cmask id='mask0_4660_8146' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='32' height='32'%3E%3Crect width='32' height='32' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_4660_8146)'%3E%3Cpath d='M9.33333 22.6666V9.33329C9.33333 8.95551 9.46111 8.63885 9.71667 8.38329C9.97222 8.12774 10.2889 7.99996 10.6667 7.99996C11.0444 7.99996 11.3611 8.12774 11.6167 8.38329C11.8722 8.63885 12 8.95551 12 9.33329V22.6666C12 23.0444 11.8722 23.3611 11.6167 23.6166C11.3611 23.8722 11.0444 24 10.6667 24C10.2889 24 9.97222 23.8722 9.71667 23.6166C9.46111 23.3611 9.33333 23.0444 9.33333 22.6666ZM14.6667 28V3.99996C14.6667 3.62218 14.7944 3.30551 15.05 3.04996C15.3056 2.7944 15.6222 2.66663 16 2.66663C16.3778 2.66663 16.6944 2.7944 16.95 3.04996C17.2056 3.30551 17.3333 3.62218 17.3333 3.99996V28C17.3333 28.3777 17.2056 28.6944 16.95 28.95C16.6944 29.2055 16.3778 29.3333 16 29.3333C15.6222 29.3333 15.3056 29.2055 15.05 28.95C14.7944 28.6944 14.6667 28.3777 14.6667 28ZM4 17.3333V14.6666C4 14.2888 4.12778 13.9722 4.38333 13.7166C4.63889 13.4611 4.95556 13.3333 5.33333 13.3333C5.71111 13.3333 6.02778 13.4611 6.28333 13.7166C6.53889 13.9722 6.66667 14.2888 6.66667 14.6666V17.3333C6.66667 17.7111 6.53889 18.0277 6.28333 18.2833C6.02778 18.5388 5.71111 18.6666 5.33333 18.6666C4.95556 18.6666 4.63889 18.5388 4.38333 18.2833C4.12778 18.0277 4 17.7111 4 17.3333ZM20 22.6666V9.33329C20 8.95551 20.1278 8.63885 20.3833 8.38329C20.6389 8.12774 20.9556 7.99996 21.3333 7.99996C21.7111 7.99996 22.0278 8.12774 22.2833 8.38329C22.5389 8.63885 22.6667 8.95551 22.6667 9.33329V22.6666C22.6667 23.0444 22.5389 23.3611 22.2833 23.6166C22.0278 23.8722 21.7111 24 21.3333 24C20.9556 24 20.6389 23.8722 20.3833 23.6166C20.1278 23.3611 20 23.0444 20 22.6666ZM25.3333 17.3333V14.6666C25.3333 14.2888 25.4611 13.9722 25.7167 13.7166C25.9722 13.4611 26.2889 13.3333 26.6667 13.3333C27.0444 13.3333 27.3611 13.4611 27.6167 13.7166C27.8722 13.9722 28 14.2888 28 14.6666V17.3333C28 17.7111 27.8722 18.0277 27.6167 18.2833C27.3611 18.5388 27.0444 18.6666 26.6667 18.6666C26.2889 18.6666 25.9722 18.5388 25.7167 18.2833C25.4611 18.0277 25.3333 17.7111 25.3333 17.3333Z' fill='%23212121'/%3E%3C/g%3E%3C/svg%3E");
  }
  
  .icon-bigger-text {
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_33_1025' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='40' height='40'%3E%3Crect width='40' height='40' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_33_1025)'%3E%3Cpath d='M24.0223 32.5V11.0416H15.753V7.50006H35.8331V11.0416H27.5638V32.5H24.0223ZM9.13446 32.5V19.2949H4.1665V15.7533H17.628V19.2949H12.66V32.5H9.13446Z' fill='%231C1B1F'/%3E%3C/g%3E%3C/svg%3E%0A");
  
  }
  
  .icon-small-text {
    width: 28px;
    height: 28px;
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_33_1025' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='40' height='40'%3E%3Crect width='40' height='40' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_33_1025)'%3E%3Cpath d='M24.0223 32.5V11.0416H15.753V7.50006H35.8331V11.0416H27.5638V32.5H24.0223ZM9.13446 32.5V19.2949H4.1665V15.7533H17.628V19.2949H12.66V32.5H9.13446Z' fill='%231C1B1F'/%3E%3C/g%3E%3C/svg%3E%0A");
  
  }
  
  .icon-line-hight {
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='41' height='40' viewBox='0 0 41 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_33_1041' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='41' height='40'%3E%3Crect x='0.333496' width='40' height='40' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_33_1041)'%3E%3Cpath d='M10.5897 32.4999L4.5 26.4102L6.25638 24.6539L9.33975 27.6859V12.314L6.25638 15.3461L4.5 13.5897L10.5897 7.50003L16.6794 13.5897L14.923 15.3461L11.8397 12.314V27.6859L14.923 24.6539L16.6794 26.4102L10.5897 32.4999ZM20.6538 30.8333V28.3333H36.1666V31.0256L20.6538 30.8333ZM20.6538 21.2499V18.75H36.1666V21.2499H20.6538ZM20.6538 11.6667V9.1667L36.1666 8.97441V11.6667H20.6538Z' fill='%231C1B1F'/%3E%3C/g%3E%3C/svg%3E%0A");
  }
  
  .icon-hide-images {
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_33_1057' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='40' height='40'%3E%3Crect width='40' height='40' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_33_1057)'%3E%3Cpath d='M34.1665 29.4166L31.6665 26.9167V8.84623C31.6665 8.71801 31.6131 8.60047 31.5063 8.4936C31.3994 8.38677 31.2819 8.33335 31.1537 8.33335H13.0832L10.5832 5.83339H31.1537C31.9955 5.83339 32.7082 6.12506 33.2915 6.70839C33.8748 7.29173 34.1665 8.00434 34.1665 8.84623V29.4166ZM33.5768 37.0897L30.6537 34.1666H8.84607C8.00418 34.1666 7.29157 33.875 6.70824 33.2916C6.12491 32.7083 5.83324 31.9957 5.83324 31.1538V9.34615L2.91016 6.42306L4.66653 4.66669L35.3332 35.3334L33.5768 37.0897ZM11.2499 27.9166L15.3845 22.436L18.7178 26.6987L20.6698 24.2083L8.3332 11.8717V31.1538C8.3332 31.282 8.38661 31.3996 8.49345 31.5064C8.60031 31.6133 8.71785 31.6667 8.84607 31.6667H28.1282L24.3781 27.9166H11.2499Z' fill='%231C1B1F'/%3E%3C/g%3E%3C/svg%3E%0A");
  }
  
  .icon-adhd-friendly {
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cmask id='mask0_4874_1390' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='24' height='25'%3E%3Crect y='0.000244141' width='24' height='24' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_4874_1390)'%3E%3Cpath d='M9.225 12.4252L10.675 14.4002C10.775 14.5336 10.9083 14.6002 11.075 14.6002C11.2417 14.6002 11.375 14.5336 11.475 14.4002L12.925 12.4252L14.35 14.4002C14.45 14.5336 14.5875 14.6002 14.7625 14.6002C14.9375 14.6002 15.075 14.5336 15.175 14.4002L17.425 11.3252C17.5917 11.0919 17.6542 10.8419 17.6125 10.5752C17.5708 10.3086 17.4333 10.0919 17.2 9.92524C16.9667 9.75858 16.7167 9.69608 16.45 9.73774C16.1833 9.77941 15.9667 9.91691 15.8 10.1502L14.75 11.5752L13.325 9.60024C13.225 9.46691 13.0875 9.40024 12.9125 9.40024C12.7375 9.40024 12.6 9.46691 12.5 9.60024L11.075 11.5752L9.625 9.60024C9.525 9.46691 9.39167 9.40024 9.225 9.40024C9.05833 9.40024 8.925 9.46691 8.825 9.60024L6.575 12.6752C6.40833 12.9086 6.34583 13.1586 6.3875 13.4252C6.42917 13.6919 6.56667 13.9086 6.8 14.0752C7.03333 14.2419 7.28333 14.3044 7.55 14.2627C7.81667 14.2211 8.03333 14.0836 8.2 13.8502L9.225 12.4252ZM12 22.0002C10.6167 22.0002 9.31667 21.7377 8.1 21.2127C6.88333 20.6877 5.825 19.9752 4.925 19.0752C4.025 18.1752 3.3125 17.1169 2.7875 15.9002C2.2625 14.6836 2 13.3836 2 12.0002C2 10.6169 2.2625 9.31691 2.7875 8.10024C3.3125 6.88358 4.025 5.82524 4.925 4.92524C5.825 4.02524 6.88333 3.31274 8.1 2.78774C9.31667 2.26274 10.6167 2.00024 12 2.00024C13.3833 2.00024 14.6833 2.26274 15.9 2.78774C17.1167 3.31274 18.175 4.02524 19.075 4.92524C19.975 5.82524 20.6875 6.88358 21.2125 8.10024C21.7375 9.31691 22 10.6169 22 12.0002C22 13.3836 21.7375 14.6836 21.2125 15.9002C20.6875 17.1169 19.975 18.1752 19.075 19.0752C18.175 19.9752 17.1167 20.6877 15.9 21.2127C14.6833 21.7377 13.3833 22.0002 12 22.0002ZM12 20.0002C14.2333 20.0002 16.125 19.2252 17.675 17.6752C19.225 16.1252 20 14.2336 20 12.0002C20 9.76691 19.225 7.87524 17.675 6.32524C16.125 4.77524 14.2333 4.00024 12 4.00024C9.76667 4.00024 7.875 4.77524 6.325 6.32524C4.775 7.87524 4 9.76691 4 12.0002C4 14.2336 4.775 16.1252 6.325 17.6752C7.875 19.2252 9.76667 20.0002 12 20.0002Z' fill='%23212121'/%3E%3C/g%3E%3C/svg%3E");
  }
  
  .icon-dyslexia-font {
    width: 30px;
    height: 30px;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' version='1.2' viewBox='0 0 31 22'%3E%3Cpath fill='currentColor' fill-rule='evenodd' d='M.5 22V1.0083333h7.2421899c6.8051611 0 11.6124768 4.3388889 11.6124768 10.4805556C19.3546667 17.6305556 14.547351 22 7.7421899 22H.5Zm2.4348742-4.31h4.8073157c5.3692097 0 9.1463863-2.8616703 9.1463863-7.27 0-4.3807776-3.7771766-7.2422222-9.1463863-7.2422222H2.9348742V17.69ZM26.2735913 4.0333333l.0114609 2.1694445h4.0126191V8.25h-4.001719L26.77 22h-3.535416L23.78 8.25h-2.4238344V6.2027778h2.55923l.0751088-2.1694445C24.0706908 1.6805556 25.6007488 0 27.697782 0 28.6896221 0 29.677687.3666667 30.5 1.0083333l-.9627285 1.6805556c-.3479788-.3666667-.9515992-.6416667-1.627768-.6416667-.8819593 0-1.6420082.825-1.6359122 1.9861111Z'/%3E%3C/svg%3E");
  }
  
  .icon-cursor {
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_33_1062' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='40' height='40'%3E%3Crect width='40' height='40' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_33_1062)'%3E%3Cpath d='M21.2594 35.7659C21.0495 35.8108 20.8395 35.8333 20.6296 35.8333H19.9998C17.8095 35.8333 15.7512 35.4177 13.8248 34.5866C11.8984 33.7555 10.2228 32.6276 8.79775 31.2029C7.37275 29.7781 6.24463 28.1027 5.41338 26.1766C4.58213 24.2506 4.1665 22.1927 4.1665 20.0028C4.1665 17.8129 4.58206 15.7545 5.41317 13.8276C6.24428 11.9007 7.3722 10.2246 8.79692 8.79919C10.2217 7.3738 11.8971 6.24537 13.8231 5.4139C15.7492 4.58242 17.8071 4.16669 19.997 4.16669C22.1869 4.16669 24.2453 4.58231 26.1722 5.41356C28.0991 6.24481 29.7752 7.37294 31.2006 8.79794C32.626 10.2229 33.7544 11.8986 34.5859 13.825C35.4174 15.7514 35.8331 17.8097 35.8331 20V20.6218C35.8331 20.829 35.8106 21.0363 35.7658 21.2436L33.3331 20.5V20C33.3331 16.2778 32.0415 13.125 29.4581 10.5416C26.8748 7.95831 23.722 6.66665 19.9998 6.66665C16.2776 6.66665 13.1248 7.95831 10.5415 10.5416C7.95813 13.125 6.66646 16.2778 6.66646 20C6.66646 23.7222 7.95813 26.875 10.5415 29.4583C13.1248 32.0416 16.2776 33.3333 19.9998 33.3333H20.4998L21.2594 35.7659ZM33.567 36.0736L26.0093 28.4999L24.1985 33.9741L19.9998 20L33.974 24.1986L28.4997 26.0095L36.0734 33.5672L33.567 36.0736Z' fill='%231C1B1F'/%3E%3C/g%3E%3C/svg%3E%0A");
  }
  
  .icon-highlight-links {
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='33' height='16' viewBox='0 0 33 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.6796 15.564H8.39758C6.30486 15.564 4.521 14.8266 3.046 13.3518C1.571 11.877 0.833496 10.0934 0.833496 8.0009C0.833496 5.90843 1.571 4.12449 3.046 2.64907C4.521 1.17365 6.30486 0.435944 8.39758 0.435944H14.6796V2.93586H8.39758C6.998 2.93586 5.80408 3.42999 4.81583 4.41824C3.82758 5.40649 3.33345 6.6004 3.33345 7.99999C3.33345 9.39957 3.82758 10.5935 4.81583 11.5817C5.80408 12.57 6.998 13.0641 8.39758 13.0641H14.6796V15.564ZM10.4168 9.24994V6.75003H22.9168V9.24994H10.4168ZM18.654 15.564V13.0641H24.936C26.3356 13.0641 27.5295 12.57 28.5177 11.5817C29.506 10.5935 30.0001 9.39957 30.0001 7.99999C30.0001 6.6004 29.506 5.40649 28.5177 4.41824C27.5295 3.42999 26.3356 2.93586 24.936 2.93586H18.654V0.435944H24.936C27.0287 0.435944 28.8126 1.17335 30.2876 2.64815C31.7626 4.12296 32.5001 5.9066 32.5001 7.99907C32.5001 10.0915 31.7626 11.8755 30.2876 13.3509C28.8126 14.8263 27.0287 15.564 24.936 15.564H18.654Z' fill='%231C1B1F'/%3E%3C/svg%3E%0A");
  }
  
  .icon-text-spacing {
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_33_1046' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='40' height='40'%3E%3Crect width='40' height='40' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_33_1046)'%3E%3Cpath d='M10.2562 35.8333L4.1665 29.7436L10.2562 23.6699L12.0222 25.4263L8.97088 28.4936H31.0287L28.003 25.4263L29.7594 23.6699L35.8331 29.7436L29.7434 35.8333L27.9774 34.0769L31.0447 30.9936H8.95488L11.9965 34.0769L10.2562 35.8333ZM12.1088 21.9231L18.7819 4.16672H21.2818L27.8908 21.9231H25.4613L23.8139 17.2244H16.237L14.5383 21.9231H12.1088ZM16.9613 15.1667H23.0383L20.1152 6.97439H19.9485L16.9613 15.1667Z' fill='%231C1B1F'/%3E%3C/g%3E%3C/svg%3E%0A");
  }
  
  .icon-moon {
    width: 32px;
    height: 32px;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cg fill='none' fill-rule='evenodd' transform='translate(-442 -200)'%3E%3Cg fill='currentColor' transform='translate(356 144)'%3E%3Cpath fill-rule='nonzero' d='M108.5 24C108.5 27.5902136 105.590214 30.5 102 30.5 98.4097864 30.5 95.5 27.5902136 95.5 24 95.5 20.4097864 98.4097864 17.5 102 17.5 105.590214 17.5 108.5 20.4097864 108.5 24zM107 24C107 21.2382136 104.761786 19 102 19 99.2382136 19 97 21.2382136 97 24 97 26.7617864 99.2382136 29 102 29 104.761786 29 107 26.7617864 107 24zM101 12.75L101 14.75C101 15.1642136 101.335786 15.5 101.75 15.5 102.164214 15.5 102.5 15.1642136 102.5 14.75L102.5 12.75C102.5 12.3357864 102.164214 12 101.75 12 101.335786 12 101 12.3357864 101 12.75zM95.7255165 14.6323616L96.7485165 16.4038616C96.9556573 16.7625614 97.4143618 16.8854243 97.7730616 16.6782835 98.1317614 16.4711427 98.2546243 16.0124382 98.0474835 15.6537384L97.0244835 13.8822384C96.8173427 13.5235386 96.3586382 13.4006757 95.9999384 13.6078165 95.6412386 13.8149573 95.5183757 14.2736618 95.7255165 14.6323616zM91.8822384 19.0244835L93.6537384 20.0474835C94.0124382 20.2546243 94.4711427 20.1317614 94.6782835 19.7730616 94.8854243 19.4143618 94.7625614 18.9556573 94.4038616 18.7485165L92.6323616 17.7255165C92.2736618 17.5183757 91.8149573 17.6412386 91.6078165 17.9999384 91.4006757 18.3586382 91.5235386 18.8173427 91.8822384 19.0244835zM90.75 25L92.75 25C93.1642136 25 93.5 24.6642136 93.5 24.25 93.5 23.8357864 93.1642136 23.5 92.75 23.5L90.75 23.5C90.3357864 23.5 90 23.8357864 90 24.25 90 24.6642136 90.3357864 25 90.75 25zM92.6323616 30.2744835L94.4038616 29.2514835C94.7625614 29.0443427 94.8854243 28.5856382 94.6782835 28.2269384 94.4711427 27.8682386 94.0124382 27.7453757 93.6537384 27.9525165L91.8822384 28.9755165C91.5235386 29.1826573 91.4006757 29.6413618 91.6078165 30.0000616 91.8149573 30.3587614 92.2736618 30.4816243 92.6323616 30.2744835zM97.0244835 34.1177616L98.0474835 32.3462616C98.2546243 31.9875618 98.1317614 31.5288573 97.7730616 31.3217165 97.4143618 31.1145757 96.9556573 31.2374386 96.7485165 31.5961384L95.7255165 33.3676384C95.5183757 33.7263382 95.6412386 34.1850427 95.9999384 34.3921835 96.3586382 34.5993243 96.8173427 34.4764614 97.0244835 34.1177616zM103 35.25L103 33.25C103 32.8357864 102.664214 32.5 102.25 32.5 101.835786 32.5 101.5 32.8357864 101.5 33.25L101.5 35.25C101.5 35.6642136 101.835786 36 102.25 36 102.664214 36 103 35.6642136 103 35.25zM108.274483 33.3676384L107.251483 31.5961384C107.044343 31.2374386 106.585638 31.1145757 106.226938 31.3217165 105.868239 31.5288573 105.745376 31.9875618 105.952517 32.3462616L106.975517 34.1177616C107.182657 34.4764614 107.641362 34.5993243 108.000062 34.3921835 108.358761 34.1850427 108.481624 33.7263382 108.274483 33.3676384zM112.117762 28.9755165L110.346262 27.9525165C109.987562 27.7453757 109.528857 27.8682386 109.321717 28.2269384 109.114576 28.5856382 109.237439 29.0443427 109.596138 29.2514835L111.367638 30.2744835C111.726338 30.4816243 112.185043 30.3587614 112.392183 30.0000616 112.599324 29.6413618 112.476461 29.1826573 112.117762 28.9755165zM113.25 23L111.25 23C110.835786 23 110.5 23.3357864 110.5 23.75 110.5 24.1642136 110.835786 24.5 111.25 24.5L113.25 24.5C113.664214 24.5 114 24.1642136 114 23.75 114 23.3357864 113.664214 23 113.25 23zM111.367638 17.7255165L109.596138 18.7485165C109.237439 18.9556573 109.114576 19.4143618 109.321717 19.7730616 109.528857 20.1317614 109.987562 20.2546243 110.346262 20.0474835L112.117762 19.0244835C112.476461 18.8173427 112.599324 18.3586382 112.392183 17.9999384 112.185043 17.6412386 111.726338 17.5183757 111.367638 17.7255165zM106.975517 13.8822384L105.952517 15.6537384C105.745376 16.0124382 105.868239 16.4711427 106.226938 16.6782835 106.585638 16.8854243 107.044343 16.7625614 107.251483 16.4038616L108.274483 14.6323616C108.481624 14.2736618 108.358761 13.8149573 108.000062 13.6078165 107.641362 13.4006757 107.182657 13.5235386 106.975517 13.8822384z' transform='translate(0 48)' stroke='currentColor' stroke-width='0.25'%3E%3C/path%3E%3Cpath d='M98.6123,60.1372 C98.6123,59.3552 98.8753,58.6427 99.3368,58.0942 C99.5293,57.8657 99.3933,57.5092 99.0943,57.5017 C99.0793,57.5012 99.0633,57.5007 99.0483,57.5007 C97.1578,57.4747 95.5418,59.0312 95.5008,60.9217 C95.4578,62.8907 97.0408,64.5002 98.9998,64.5002 C99.7793,64.5002 100.4983,64.2452 101.0798,63.8142 C101.3183,63.6372 101.2358,63.2627 100.9478,63.1897 C99.5923,62.8457 98.6123,61.6072 98.6123,60.1372' transform='translate(3 11)'%3E%3C/path%3E%3C/g%3E%3Cpolygon points='444 228 468 228 468 204 444 204'%3E%3C/polygon%3E%3C/g%3E%3C/svg%3E");
  }
  
  .icon-sun {
    width: 32px;
    height: 32px;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cg fill='none' fill-rule='evenodd' transform='translate(-440 -200)'%3E%3Cpath fill='currentColor' fill-rule='nonzero' stroke='currentColor' stroke-width='0.5' d='M102,21 C102,18.1017141 103.307179,15.4198295 105.51735,13.6246624 C106.001939,13.2310647 105.821611,12.4522936 105.21334,12.3117518 C104.322006,12.1058078 103.414758,12 102.5,12 C95.8722864,12 90.5,17.3722864 90.5,24 C90.5,30.6277136 95.8722864,36 102.5,36 C106.090868,36 109.423902,34.4109093 111.690274,31.7128995 C112.091837,31.2348572 111.767653,30.5041211 111.143759,30.4810139 C106.047479,30.2922628 102,26.1097349 102,21 Z M102.5,34.5 C96.7007136,34.5 92,29.7992864 92,24 C92,18.2007136 96.7007136,13.5 102.5,13.5 C102.807386,13.5 103.113925,13.5136793 103.419249,13.5407785 C101.566047,15.5446378 100.5,18.185162 100.5,21 C100.5,26.3198526 104.287549,30.7714322 109.339814,31.7756638 L109.516565,31.8092927 C107.615276,33.5209452 105.138081,34.5 102.5,34.5 Z' transform='translate(354.5 192)'%3E%3C/path%3E%3Cpolygon points='444 228 468 228 468 204 444 204'%3E%3C/polygon%3E%3C/g%3E%3C/svg%3E");
  }
  
  .icon-invert {
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_33_1073' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='40' height='40'%3E%3Crect width='40' height='40' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_33_1073)'%3E%3Cpath d='M20 34.1666C16.5299 34.1666 13.5791 32.9674 11.1474 30.5689C8.71581 28.1704 7.5 25.2639 7.5 21.8494C7.5 20.1122 7.82853 18.5192 8.48558 17.0705C9.14264 15.6218 10.0342 14.3184 11.1602 13.1603L20 4.48724L28.8397 13.1603C29.9657 14.3184 30.8573 15.6298 31.5143 17.0946C32.1714 18.5593 32.4999 20.1442 32.4999 21.8494C32.4999 25.2639 31.2841 28.1704 28.8525 30.5689C26.4209 32.9674 23.47 34.1666 20 34.1666ZM20 31.6667V7.99999L12.9166 15C11.9444 15.9167 11.2152 16.9541 10.7291 18.1122C10.243 19.2703 9.99996 20.516 9.99996 21.8494C9.99996 24.5438 10.9722 26.8536 12.9166 28.7788C14.8611 30.7041 17.2222 31.6667 20 31.6667Z' fill='%231C1B1F'/%3E%3C/g%3E%3C/svg%3E%0A");
  }
  
  /* icon set end */
  .ux4g-bg-white {
    background: var(--color-white) !important;
    filter: none !important;
  }
  
  #uw-main {
    right: -530px;
  }
  
  .ux4g-bg-white #uw-main {
    filter: invert(1) !important;
    box-shadow: 0 15px 30px rgb(2 2 2 / 36%) !important;
  }
  
  #accessibilityButtons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    /* Adjust as needed */
  }
  
  .adjust-button {
    cursor: pointer;
    padding: 5px;
    background-color: #ddd;
  }
  
  .text-button {
    font-weight: bold;
    padding: 5px;
  }
  
  .uw-widget-custom-trigger {
    border: none;
    border-radius: 70px;
    bottom: 24px;
    cursor: pointer;
    height: 70px;
    padding: 18px;
    position: fixed;
    right: 20px;
    display: flex;
    overflow: hidden;
    align-items: center;
    width: auto;
    max-width: 70px;
    transition: all 400ms;
    color: var(--color-white);
    background-color: var(--color-dark-blue-1);
    text-align: left;
    z-index: 99999;
  }
  
  .uw-widget-custom-trigger:hover {
    max-width: 300px;
  }
  
  .uw-widget-custom-trigger:hover span {
    opacity: 1;
  }
  
  .uw-widget-custom-trigger span {
    white-space: nowrap;
    padding-left: 5px;
    font-size: 16px;
    font-family: 'Roboto', sans-serif;
    opacity: 0;
  }
  
  .uwaw-close {
    background-image: url("data:image/svg+xml,%0A%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.53341 19.3333L0.666748 17.4667L8.13341 10L0.666748 2.53334L2.53341 0.666672L10.0001 8.13334L17.4667 0.666672L19.3334 2.53334L11.8667 10L19.3334 17.4667L17.4667 19.3333L10.0001 11.8667L2.53341 19.3333Z' fill='white'/%3E%3C/svg%3E%0A");
    background-repeat: no-repeat;
    background-position: center;
    padding: 0;
    width: 30px;
    height: 30px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    transition: all 1s;
    overflow: hidden;
    background-color: transparent;
    outline: 2px solid transparent;
    border: 0;
  }
  
  button.uwaw-close:hover,
  button.uwaw-close:focus {
    background-color: transparent !important;
  }
  
  button.uwaw-close:focus {
    outline-color: var(--color-white) !important;
  }
  
  
  .uwaw-close svg {
    width: 13px;
    height: 13px;
  }
  
  .uwaw-close:hover i {
  
    color: var(--color-white);
  }
  
  .uwaw {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    background: var(--color-white);
    max-width: 450px;
    min-height: 100%;
    width: 100%;
    /* box-shadow: 0 22px 110px rgba(180,191,208,.56); */
    filter: drop-shadow(0 10px 20px rgb(2 2 2 / 20%));
    max-height: 530px;
    /* right: -530px; */
    right: 0;
    position: fixed;
    z-index: 999999;
    bottom: 0;
    transition: all 0.3s;
    font-family: "Noto Sans", sans-serif !important;
    overflow: hidden;
  }
  
  .h-scroll::-webkit-scrollbar {
    background-color: var(--color-black);
    width: 4px;
  }
  
  .h-scroll::-webkit-scrollbar-track {
    background-color: #a7a7a7;
  }
  
  
  .h-scroll::-webkit-scrollbar-thumb {
    background-color: var(--color-dark-blue-1);
    border-radius: 10px;
  }
  
  .uwaw-body {
    background: #ECEEF5;
    position: relative;
    z-index: 1;
    padding: 18px;
    margin-bottom: 15px;
  
  
  }
  
  .reset-panel {
    position: absolute;
    width: 100%;
    bottom: 10px;
  }
  
  .h-scroll {
    /* max-height: 630px; */
    height: calc(100vh - 153px) !important;
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  .second-panel {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    gap: 20px;
    justify-content: space-between;
    background-color: var(--color-dark-blue-1);
  }
  
  .second-panel h2 {
    font-size: 20px;
    font-weight: 300;
    color: var(--color-white);
    margin: 0;
  }
  
  .uwaw-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    padding-bottom: 15px;
  }
  
  .uwaw-features__item__i {
    position: relative;
    width: 100%;
    height: 124px;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: var(--color-white);
    border-radius: 12px;
    border: 2px solid var(--color-white);
    padding: 5px;
    transition: border-color .15s ease;
  }
  
  .uwaw-features__item__icon {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    /* margin-top: 17px; */
  }
  
  .uwaw-features__item__name {
    font-size: 13px;
    font-weight: 600;
    color: var(--color-black4);
  }
  
  .feature-active {
    /* Add styles for the active state of the parent div */
    position: relative;
  }
  
  /* active icon */
  .feature-active .uwaw-features__item__icon svg,
  .feature-active .uwaw-features__item__name,
  .feature-active .light_dark_icon i {
    color: var(--color-dark-blue-1);
  }
  
  .feature-active .icon-bigger-text,
  .feature-active .icon-small-text,
  .feature-active .icon-line-hight,
  .feature-active .icon-hide-images,
  .feature-active .icon-dyslexia-font,
  .feature-active .icon-cursor,
  .feature-active .icon-highlight-links,
  .feature-active .icon-text-spacing,
  .feature-active .icon-moon,
  .feature-active .icon-sun,
  .feature-active .icon-invert,
  .feature-active .icon-speaker,
  .feature-active .icon-adhd-friendly {
    background-color: var(--color-dark-blue-1);
  }
  
  /* disabled btn */
  #btn-small-text:disabled .icon-small-text,
  #btn-s9:disabled .icon-bigger-text {
    background-color: rgba(16, 16, 16, 0.3);
  }
  
  .uwaw #btn-small-text:disabled,
  .uwaw #btn-s9:disabled {
    border: transparent;
  }
  
  .feature-active .uwaw-features__item__i {
    border: 2px solid var(--color-dark-blue-1);
    box-shadow: 0 0 0 5px rgba(0, 107, 230, 0.1);
  
  }
  
  .uwaw-features__item__steps span {
    /* Add styles for the step span tags */
    margin: 0 5px;
    cursor: pointer;
  }
  
  .tick-active {
    background-color: var(--color-dark-blue-1);
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }
  
  .tick-active svg {
    font-size: 12px;
    width: 12px;
  }
  
  .uwaw-features__item__enabled {
    position: absolute;
    right: 10px;
    top: 10px;
    display: none;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 8' width='100%25' height='100%25'%3E%3Cpath fill='' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.75' d='m1.5 4.5 2 2 5-5'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-size: 12px;
    background-position: center;
  
  }
  
  .uwaw-features__item__steps {
    display: flex;
    width: 75%;
    opacity: 0;
    transition: opacity .15s ease, visibility .15s ease;
    visibility: hidden;
    position: absolute;
    bottom: 6px;
  }
  
  .uwaw-features__item__steps.featureSteps-visible {
    opacity: 1;
    visibility: visible;
  }
  
  .uwaw-features__step {
    width: 100%;
    border-radius: 10px;
    display: block;
    height: 3px;
    position: relative;
    background: #1937B247;
  }
  
  .uwaw-features__step.active {
    background: var(--color-dark-blue-1);
  
  }
  
  /* er */
  .text-center {
    text-align: center;
  }
  
  .d-flex {
    display: flex;
  }
  
  .d-column {
    flex-direction: column;
  }
  
  /* Full-width textarea */
  
  
  /* Set a style for the submit/send button */
  .uwaw-features__item__name .btn,
  .chat-container .btn {
    background-color: #e1e1e1;
    color: white;
    font-size: 22px;
    padding: 10px 20px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
    float: right;
  }
  
  /* Add a red background color to the cancel button */
  .chat-container .cancel {
    background-color: #a066cc;
    position: absolute;
    top: -15px;
    right: -9px;
    padding: 0;
    width: 30px;
    height: 30px;
    font-size: 12px;
    border: solid 3px
  }
  
  .chat-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
  }
  
  .chat-grid .chat-card {
    border-radius: 10px;
    background: var(--color-white);
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: solid 2px var(--color-white);
    box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075) !important;
  }
  
  .chat-grid .chat-card:focus {
    border-color: #eea40cc4;
  }
  
  /* text speech */
  #readSelectedText {
    position: absolute;
    display: none;
    margin-top: -20px;
  }
  
  /* hide image */
  
  .uwaw-features__item__name button,
  .chat-grid button {
    width: 100%;
    height: 100%;
    background: var(--color-white);
    border-radius: 10px;
    border-color: transparent;
    cursor: pointer;
    transition: all 1s;
    outline: none;
    font-weight: bold;
  }
  
  .uwaw button:focus,
  .uwaw button:hover {
    border-color: var(--color-dark-blue-1);
    outline-color: var(--color-dark-blue-1);
    transition: all 1s;
    background-color: #ecd0ff6b;
  }
  
  .align-items-center {
    align-items: center;
  }
  
  /* light mode */
  .light_mode {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  
  .checkbox-label {
    font-size: 28px;
    position: relative;
    padding: 5px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: none;
  }
  
  .light_dark_icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
  }
  
  body.dark .light_dark_icon .fa-moon-stars {
    display: block;
  }
  
  .light_dark_icon i {
    font-size: 0;
  }
  
  .light_dark_icon .fa-moon-stars {
    color: var(--color-black);
    display: none;
  }
  
  body.dark .light_dark_icon .fa-sun {
    display: none;
  }
  
  .light_dark_icon .fa-sun {
    color: var(--color-black);
  }
  
  /* all theme update in dark mode  */
  body.dark *:not(.uwaw, .uwaw *, .uw-widget-custom-trigger, .uw-widget-custom-trigger img, .uw-widget-custom-trigger span) {
    color: var(--color-white) !important;
    background-color: var(--color-black3) !important;
  }
  
  
  .copyrights-accessibility {
    background-color: var(--color-white);
    padding: 0 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .copyrights-accessibility a {
    width: 100%;
    padding: 5px 0;
    text-decoration: none;
    color: var(--color-black);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    align-items: center;
  }
  
  .copyrights-accessibility a span {
    font-size: 14px;
  }
  
  .copyrights-accessibility a img {
    height: 13px;
  }
  
  /* add class in html tag */
  .ux4g-bg-cursor * {
    cursor: url('data:image/svg+xml,<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="29.188px" height="43.625px" viewBox="0 0 29.188 43.625" enable-background="new 0 0 29.188 43.625" xml:space="preserve"><g><polygon fill="%23FFFFFF" stroke="%23D9DAD9" stroke-width="1.1406" stroke-miterlimit="10" points="2.8,4.549 26.847,19.902 16.964,22.701 24.239,37.749 18.278,42.017 9.741,30.724 1.138,35.809 "/><g><g><g><path fill="%23212627" d="M29.175,21.155c0.071-0.613-0.165-1.253-0.635-1.573L2.165,0.258c-0.424-0.32-0.988-0.346-1.435-0.053C0.282,0.497,0,1.03,0,1.617v34.171c0,0.613,0.306,1.146,0.776,1.439c0.471,0.267,1.059,0.213,1.482-0.16l7.482-6.344l6.847,12.155c0.259,0.48,0.729,0.746,1.2,0.746c0.235,0,0.494-0.08,0.706-0.213l6.988-4.585c0.329-0.213,0.565-0.586,0.659-1.013c0.094-0.426,0.024-0.88-0.188-1.226l-6.376-11.382l8.611-2.745C28.705,22.274,29.105,21.768,29.175,21.155z M16.964,22.701c-0.424,0.133-0.776,0.506-0.941,0.96c-0.165,0.48-0.118,1.013,0.118,1.439l6.588,11.781l-4.541,2.985l-6.894-12.315c-0.212-0.373-0.541-0.64-0.941-0.72c-0.094-0.027-0.165-0.027-0.259-0.027c-0.306,0-0.588,0.107-0.847,0.32L2.8,32.59V4.549l21.599,15.806L16.964,22.701z"/></g></g></g></g></svg>'), auto !important;
  
  }
  
  .ux4g-font-df *:not(.fal, .fa, .fas) {
    font-family: 'Open-Dyslexic', sans-serif !important;
  }
  
  .ux4g-font-df .uwaw-features__item__name,
  .ux4g-font-df .second-panel strong,
  .ux4g-copyright,
  .btn-reset-all .reset-btn-text,
  .ux4g-font-df .second-panel h3,
  .ux4g-font-df .second-panel strong,
  .ux4g-font-df .lang_head span,
  .ux4g-font-df .language_drop select,
  .ux4g-font-df .language_drop select option,
  .ux4g-font-df .uw-widget-custom-trigger span {
    font-family: "Noto Sans", sans-serif !important;
  }
  
  /* btn-reset-all */
  .btn-reset-all {
    background-color: #ECD0FF;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 1s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 12px;
    color: var(--color-black4);
    font-size: 14px;
    transition: all 300ms;
    width: 100%;
    font-weight: 500;
  }
  
  .btn-reset-all img {
    height: 30px;
    filter: brightness(0) invert(1);
  
  }
  
  .reset-icon {
    background-color: var(--color-black4);
    mask-image: url("data:image/svg+xml,%0A%3Csvg width='16' height='19' viewBox='0 0 16 19' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 18.95C4.98333 18.7 3.3125 17.8208 1.9875 16.3125C0.6625 14.8042 0 13.0333 0 11C0 9.9 0.216667 8.84583 0.65 7.8375C1.08333 6.82917 1.7 5.95 2.5 5.2L3.925 6.625C3.29167 7.19167 2.8125 7.85 2.4875 8.6C2.1625 9.35 2 10.15 2 11C2 12.4667 2.46667 13.7625 3.4 14.8875C4.33333 16.0125 5.53333 16.7 7 16.95V18.95ZM9 18.95V16.95C10.45 16.6833 11.6458 15.9917 12.5875 14.875C13.5292 13.7583 14 12.4667 14 11C14 9.33333 13.4167 7.91667 12.25 6.75C11.0833 5.58333 9.66667 5 8 5H7.925L9.025 6.1L7.625 7.5L4.125 4L7.625 0.5L9.025 1.9L7.925 3H8C10.2333 3 12.125 3.775 13.675 5.325C15.225 6.875 16 8.76667 16 11C16 13.0167 15.3375 14.7792 14.0125 16.2875C12.6875 17.7958 11.0167 18.6833 9 18.95Z' fill='%231937B2'/%3E%3C/svg%3E%0A");
    mask-repeat: no-repeat;
    background-size: cover;
    width: 27px;
    height: 19px;
    transform: scaleX(-1);
  }
  
  
  .ux4g-copyright {
    letter-spacing: 0 !important;
  }
  
  #imageHideBg.image-hide *:not(.uwaw-features__item__enabled, .uwaw-close, .uw-widget-custom-trigger, .lang i, .language_drop select) {
    background-image: none !important;
  }
  
  /* lang */
  .lang {
    padding: 12px;
    border-radius: 16px;
    background-color: var(--color-white);
    margin-bottom: 18px;
  }
  
  .lang_head {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 16px;
    font-weight: 400;
    color: var(--color-black);
  }
  
  .lang {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .lang i {
    width: 52px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background-color: var(--color-dark-blue-1);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    background-image: url("data:image/svg+xml,%0A%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21.056 12H19.056C18.7907 12 18.5364 12.1054 18.3489 12.2929C18.1613 12.4804 18.056 12.7348 18.056 13C18.056 13.2652 18.1613 13.5196 18.3489 13.7071C18.5364 13.8946 18.7907 14 19.056 14V16H17.87C17.9888 15.6798 18.0513 15.3415 18.055 15C18.0549 14.3396 17.837 13.6977 17.435 13.1738C17.033 12.6499 16.4693 12.2733 15.8314 12.1024C15.1935 11.9314 14.5171 11.9758 13.907 12.2285C13.2969 12.4812 12.7872 12.9281 12.457 13.5C12.3903 13.6138 12.3467 13.7396 12.3289 13.8703C12.311 14.0009 12.3192 14.1339 12.3529 14.2614C12.3866 14.3889 12.4452 14.5084 12.5253 14.6132C12.6054 14.7179 12.7054 14.8058 12.8196 14.8718C12.9338 14.9377 13.06 14.9804 13.1907 14.9974C13.3215 15.0144 13.4544 15.0054 13.5817 14.9708C13.7089 14.9363 13.8281 14.8769 13.9323 14.7961C14.0366 14.7153 14.1238 14.6147 14.189 14.5C14.2767 14.348 14.403 14.2218 14.555 14.134C14.707 14.0462 14.8794 14 15.055 14C15.3202 14 15.5745 14.1054 15.7621 14.2929C15.9496 14.4804 16.055 14.7348 16.055 15C16.055 15.2652 15.9496 15.5196 15.7621 15.7071C15.5745 15.8946 15.3202 16 15.055 16C14.7897 16 14.5354 16.1054 14.3479 16.2929C14.1603 16.4804 14.055 16.7348 14.055 17C14.055 17.2652 14.1603 17.5196 14.3479 17.7071C14.5354 17.8946 14.7897 18 15.055 18C15.3202 18 15.5745 18.1054 15.7621 18.2929C15.9496 18.4804 16.055 18.7348 16.055 19C16.055 19.2652 15.9496 19.5196 15.7621 19.7071C15.5745 19.8946 15.3202 20 15.055 20C14.8794 20 14.707 19.9538 14.555 19.866C14.403 19.7783 14.2767 19.652 14.189 19.5C14.1238 19.3854 14.0366 19.2847 13.9323 19.2039C13.8281 19.1231 13.7089 19.0637 13.5817 19.0292C13.4544 18.9946 13.3215 18.9856 13.1907 19.0026C13.06 19.0196 12.9338 19.0623 12.8196 19.1282C12.7054 19.1942 12.6054 19.2821 12.5253 19.3868C12.4452 19.4916 12.3866 19.6112 12.3529 19.7387C12.3192 19.8662 12.311 19.9991 12.3289 20.1297C12.3467 20.2604 12.3903 20.3862 12.457 20.5C12.7872 21.0719 13.2969 21.5188 13.907 21.7715C14.5171 22.0242 15.1935 22.0686 15.8314 21.8976C16.4693 21.7267 17.033 21.3501 17.435 20.8262C17.837 20.3023 18.0549 19.6604 18.055 19C18.0513 18.6585 17.9888 18.3202 17.87 18H19.055V21C19.055 21.2652 19.1603 21.5196 19.3479 21.7071C19.5354 21.8946 19.7897 22 20.055 22C20.3202 22 20.5745 21.8946 20.7621 21.7071C20.9496 21.5196 21.055 21.2652 21.055 21V14C21.3202 14 21.5745 13.8946 21.7621 13.7071C21.9496 13.5196 22.055 13.2652 22.055 13C22.055 12.7348 21.9496 12.4804 21.7621 12.2929C21.5745 12.1054 21.3212 12 21.056 12ZM9.08496 11.243C9.1161 11.3712 9.1723 11.492 9.25032 11.5983C9.32834 11.7047 9.42664 11.7946 9.53955 11.8628C9.65247 11.9311 9.77776 11.9763 9.90822 11.9958C10.0387 12.0154 10.1717 12.009 10.2997 11.977C10.4277 11.9449 10.548 11.8878 10.6538 11.8091C10.7597 11.7303 10.8489 11.6313 10.9163 11.5179C10.9837 11.4045 11.028 11.2789 11.0466 11.1483C11.0653 11.0177 11.0579 10.8847 11.025 10.757L9.26796 3.727C9.14505 3.23319 8.86047 2.79468 8.45953 2.4813C8.05859 2.16792 7.56434 1.99768 7.05546 1.99768C6.54658 1.99768 6.05232 2.16792 5.65138 2.4813C5.25044 2.79468 4.96587 3.23319 4.84296 3.727L3.08496 10.757C3.05199 10.8847 3.04462 11.0177 3.06327 11.1483C3.08193 11.2789 3.12624 11.4045 3.19365 11.5179C3.26106 11.6313 3.35025 11.7303 3.45607 11.8091C3.56189 11.8878 3.68225 11.9449 3.81022 11.977C3.93818 12.009 4.07123 12.0154 4.20169 11.9958C4.33215 11.9763 4.45745 11.9311 4.57036 11.8628C4.68328 11.7946 4.78157 11.7047 4.85959 11.5983C4.93761 11.492 4.99381 11.3712 5.02496 11.243L5.58496 9H8.52496L9.08496 11.243ZM6.08596 7L6.78296 4.213C6.80398 4.15762 6.84135 4.10993 6.89011 4.07628C6.93887 4.04264 6.99671 4.02461 7.05596 4.02461C7.1152 4.02461 7.17304 4.04264 7.2218 4.07628C7.27056 4.10993 7.30794 4.15762 7.32896 4.213L8.02596 7H6.08596ZM14.056 7H15.056C15.3211 7.00027 15.5753 7.10571 15.7628 7.29319C15.9503 7.48067 16.0557 7.73487 16.056 8V9C16.056 9.26522 16.1613 9.51958 16.3489 9.70711C16.5364 9.89465 16.7907 10 17.056 10C17.3212 10 17.5755 9.89465 17.7631 9.70711C17.9506 9.51958 18.056 9.26522 18.056 9V8C18.0552 7.2046 17.7388 6.442 17.1764 5.87956C16.614 5.31712 15.8514 5.0008 15.056 5H14.056C13.7907 5 13.5364 5.10536 13.3489 5.2929C13.1613 5.48043 13.056 5.73479 13.056 6C13.056 6.26522 13.1613 6.51958 13.3489 6.70711C13.5364 6.89465 13.7907 7 14.056 7ZM10.056 16H9.05596C8.79082 15.9997 8.53662 15.8943 8.34914 15.7068C8.16166 15.5193 8.05622 15.2651 8.05596 15V14C8.05596 13.7348 7.9506 13.4804 7.76306 13.2929C7.57553 13.1054 7.32117 13 7.05596 13C6.79074 13 6.53639 13.1054 6.34885 13.2929C6.16131 13.4804 6.05596 13.7348 6.05596 14V15C6.05675 15.7954 6.37308 16.558 6.93551 17.1204C7.49795 17.6829 8.26055 17.9992 9.05596 18H10.056C10.3212 18 10.5755 17.8946 10.7631 17.7071C10.9506 17.5196 11.056 17.2652 11.056 17C11.056 16.7348 10.9506 16.4804 10.7631 16.2929C10.5755 16.1054 10.3212 16 10.056 16Z' fill='white'/%3E%3C/svg%3E%0A");
  }
  
  body[data-gr-ext-installed] {
    top: 0 !important;
  }
  
  .language_drop select {
    border: 0;
    font-size: 16px;
    color: #1A73E9 !important;
    appearance: none;
    background-image: url("data:image/svg+xml,%0A%3Csvg width='32' height='32' viewBox='0 0 32 32' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_33_1013' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='32' height='32'%3E%3Crect width='32' height='32' fill='%23D9D9D9'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_33_1013)'%3E%3Cpath d='M16.0002 20L9.3335 13.3333H22.6668L16.0002 20Z' fill='%231C1B1F'/%3E%3C/g%3E%3C/svg%3E%0A");
    background-repeat: no-repeat;
    background-position: right center;
    padding: 10px 40px 10px 10px;
    cursor: pointer;
    width: 170px;
  }
  
  .language_drop select:is(:focus, :hover) {
    outline: none;
    border: none;
  }
  
  /* scroll */
  
  .language_drop select::-webkit-scrollbar-thumb {
    background-color: var(--color-dark-blue-1);
    border-radius: 5px;
  }
  
  .language_drop select::-webkit-scrollbar {
    background-color: var(--color-white);
    width: 5px;
  }
  
  
  .language_drop select::-webkit-scrollbar-track {
    background-color: #a7a7a7;
  }
  
  .language_drop select::-webkit-scrollbar-track:hover {
    background-color: #a7a7a7;
  }
  
  
  /* scroll end*/
  .language_drop select option {
    color: var(--color-black);
  }
  
  
  .VIpgJd-ZVi9od-ORHb-OEVmcd {
    display: none;
  }
  
  .skiptranslate span {
    display: none;
  }
  
  .skiptranslate:not(.goog-te-combo) {
    font-size: 0;
  }
  
  
  @media (max-width: 767px) {
    .uwaw-features {
        grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
    }
  
    .uwaw {
        max-width: 100%;
    }
  
    .second-panel h3 {
        font-size: 24px;
    }
  
    .copyrights-accessibility a span {
        font-size: 16px;
    }
  }
  
  .highlight-links a:not(#uw-main a),
  .highlight-links button:not(#uw-main button) {
    background: #444701 !important;
    color: yellow !important;
  }
  
  .highlight-links a span:not(#uw-main a span) {
    color: yellow !important;
  }
  
  .highlight-links a img:not(#uw-main a img) {
    text-decoration: underline !important;
    transition: box-shadow 0.3s;
    color: rgb(255, 255, 0) !important;
    background-color: #444701 !important;
  }
  
  .dyslexia-mode *:not(#uw-main *):not(#uw-main):not(.uw-widget-custom-trigger span) {
    font-family: 'Open-Dyslexic', sans-serif !important;
  }
  
  .hide-images img:not(#uw-main img):not(#uw-widget-custom-trigger img) {
    visibility: hidden !important;
  }
  
  .span-visible {
    visibility: visible;
    padding-top: 10px;
    position: relative;
    transform: translateX(0px);
  }
  
  /* .form-check {
    padding-left: 0px !important;
  } */
  
  .hide-images *:not(#uw-main *):not(#uw-main) {
    background-image: none !important;
  }
  
  /* Apply custom cursor to everything except #uw-main */
  .custom-cursor,
  .custom-cursor * {
    cursor: url('data:image/svg+xml,<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="29.188px" height="43.625px" viewBox="0 0 29.188 43.625" enable-background="new 0 0 29.188 43.625" xml:space="preserve"><g><polygon fill="%23FFFFFF" stroke="%23D9DAD9" stroke-width="1.1406" stroke-miterlimit="10" points="2.8,4.549 26.847,19.902 16.964,22.701 24.239,37.749 18.278,42.017 9.741,30.724 1.138,35.809 "/><g><g><g><path fill="%23212627" d="M29.175,21.155c0.071-0.613-0.165-1.253-0.635-1.573L2.165,0.258c-0.424-0.32-0.988-0.346-1.435-0.053C0.282,0.497,0,1.03,0,1.617v34.171c0,0.613,0.306,1.146,0.776,1.439c0.471,0.267,1.059,0.213,1.482-0.16l7.482-6.344l6.847,12.155c0.259,0.48,0.729,0.746,1.2,0.746c0.235,0,0.494-0.08,0.706-0.213l6.988-4.585c0.329-0.213,0.565-0.586,0.659-1.013c0.094-0.426,0.024-0.88-0.188-1.226l-6.376-11.382l8.611-2.745C28.705,22.274,29.105,21.768,29.175,21.155z M16.964,22.701c-0.424,0.133-0.776,0.506-0.941,0.96c-0.165,0.48-0.118,1.013,0.118,1.439l6.588,11.781l-4.541,2.985l-6.894-12.315c-0.212-0.373-0.541-0.64-0.941-0.72c-0.094-0.027-0.165-0.027-0.259-0.027c-0.306,0-0.588,0.107-0.847,0.32L2.8,32.59V4.549l21.599,15.806L16.964,22.701z"/></g></g></g></g></svg>') 10 10, auto !important;
  }
  
  /* Apply invert to the whole page */
  html.invert-colors {
    filter: invert(1) !important;
    background: var(--color-white) !important;
  }
  
  .span-visible {
    visibility: visible !important;
  }
  
  .span-invisible {
    visibility: hidden;
  }
  
  .highlight-links a::selection,
  .dark-mode::selection {
    background: yellow;    
    color: black;    
  }
  
  /* Apply dark mode background and text color */
  
  body.dark-mode {
    color: var(--color-white);
    background-color: var(--color-black3);
    border-color: rgb(255, 255, 255) !important;
  }
  
  /* Prevent dark mode changes for images */
  body.dark-mode img:not(#uw-main *):not(#uw-main):not(#uw-widget-custom-trigger *):not(#uw-widget-custom-trigger) {
    filter: none;
  }
  
  /* Prevent dark mode on elements with a background image */
  body.dark-mode div *:has([style*="background"], [style*="background-image"], img) {
    filter: none !important;
    background-color: inherit;
  }
  
  /* ðŸ”¥ Fix: Ensure black text becomes white */
  body.dark-mode * :not(#uw-main *):not(#uw-main):not(#uw-widget-custom-trigger *):not(#uw-widget-custom-trigger) {
    color: var(--color-white) !important;
  }
  
  /* ðŸ”¥ If an element's text was already black, change it to white */
  body.dark-mode *[style*="color: black"] {
    color: var(--color-white) !important;
  }
  
  /* Ensure links and buttons don't become invisible */
  body.dark-mode a:not(#uw-main *):not(#uw-main):not(#uw-widget-custom-trigger *):not(#uw-widget-custom-trigger),
  /* body.dark-mode button:not(#uw-main *):not(#uw-main):not(#uw-widget-custom-trigger *):not(#uw-widget-custom-trigger), */
  body.dark-mode a *:not(#uw-main *):not(#uw-main):not(#uw-widget-custom-trigger *):not(#uw-widget-custom-trigger) {
    color: rgb(255, 255, 0) !important;
    /* Yellow color */
  }
  
  /* body.dark-mode *:not(#uw-main *):not(#uw-widget-custom-trigger *):not([style*="color:"]) {
    color: var(--color-white) !important;
  } */
  
  body.dark-mode span[style*="color:"],
  body.dark-mode p[style*="color:"],
  body.dark-mode div[style*="color:"] {
    color: var(--color-white) !important;
    /* Keep user-defined colors */
  }
  
  body.dark-mode *[style*="color:"] {
    color: var(--color-white) !important;
  }
  
  body.dark-mode *[style*="background"],
  body.dark-mode *[style*="background-image"] {
    background-color: inherit !important;
    filter: none !important;
    color: inherit !important;
  }