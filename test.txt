const urls = [
    "https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Roboto:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i",
    "https://udyamregistration.gov.in/assets/vendor/bootstrap/css/bootstrap.min.css",
    "https://udyamregistration.gov.in/assets/vendor/boxicons/css/boxicons.min.css",
    "https://udyamregistration.gov.in/assets/vendor/icofont/icofont.min.css",
    "https://udyamregistration.gov.in/assets/vendor/owl.carousel/assets/owl.carousel.min.css",
    "https://udyamregistration.gov.in/assets/vendor/venobox/venobox.css",
    "https://udyamregistration.gov.in/assets/vendor/aos/aos.css",
    "https://udyamregistration.gov.in/assets/css/style.css",
    "https://udyamregistration.gov.in/WebResource.axd?d=HssZ55r-YQGVRqZIygPpAA8-mSvFISHNH64Mhogg7NHdWbEzTKZSjlDiptGdMvAWB9eRPLKFT9JuSF3bNH-Qg3Q1zhmHD_W7wwyCfsh4rdTjgSaA-yVMXvIZrhBB3yAL9T4OR2r7m1kcJpPUtdk3MIolK1w1&t=637110559240000000",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=ZRl_HRH7Wke0zmP7SmEl0sZYQXajrBJXxRRKjecVYNtNYMy970nsJzRLuzMB4em4VfayNHJ4H9CGAykq7nZ0wW8jveyMIDdfhyPPp_mKh1BNWQNSyuGxRH5Fx3QYOGkZ9MLAp5iOSpeNH9WYOQHe5Mey8LM1&t=ffffffffba22f784",
    "https://udyamregistration.gov.in/WebResource.axd?d=BayMCezD8XvrvgveJL7NURf1ZwggoPVyM7Azy9RELEDeozEDadElOKK1OmtKleKTbkwC5MxyRR4izP-NhA9-m7Rjzn41&t=638568802371266408",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=ZcC4pqWDloVZ_h8bxX2LIa6VQrRMImWwX4COhUwztNeZe0XU-5LdMBdPxmX1UTsqEeR-38ISjw-vdHM5kxCpdBlY8JbMtCzMhDu0G5Rym63s71eJwKRF7yrPYppaKBwNjGOMEYxxr4TTPz8k7ZUPGmhG_wI1&t=ffffffffc820c398",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=H5h_4QA61SxxXAxj511af0BnAMT5qO8EtoCzNK83omYo7L8yhx8VocAT7EDx7BnQ9L7N3e7_rZ-d30pMADP_Me13UYW9_O6GP4b7BobwB3zLIgfuVz_DJtW2_wVymSvo6RDpfnk79DQcVRxskdIpc-62wPR0x0QxCuRkvcszaFWsHFVo0&t=ffffffffc820c398",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=SZAF229YE1BzUkr8dWnGQ6frmjznn1s--Ayb928-6iH4oYBE-Qy3B5qaj76OOAIXHRT9xclKyJkAnJ8zbBT0o0xv9NfEZoQU4OfZAmAJCmzIkBdm1iwpgBtjjsyvGLlq_uchjjXsDeykbOHzgRR7SMhYeXo1&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=4WMj220ly8_LUuALPAEmhGazPiiGtzj_Fizqr8BSkaDvfljcR_Z4WWRNq_NlZGCwJ9hvvlnquoe9-zmDIBA9ukY-pHR0i3nKuv__iIgT8qVcVUdseCNVwKYVLQ4dUUe3kykDp5rSkgz_xDMvvo7rDa_OFjc1&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=leAbOU-Oohss4A1vmGoupOz81ye78rpVL51k4dII8hYcU9OAEfMTUsf-LaI1WH23plSxyCNkZUrDY0xqGrAxxyKmjfLAbt8x7KVj2bYPOgUt6KjMde2ukT0xsWqiMSvz0yITN98BdktmGLbR715vjaaaSWhvyAtOlYPURCThDu1IPZwR0&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=sO_jTt2J_khP0ep7ou03lyGVw-dYCRtGwUaJKH1rUctx77jMO4_o9FnpsztVAnwg7vLkd3TjGHNZa_TdIImK666iynkeHN_8dTpNVuhbDQhPD4t0g-m4LyeStIX8poe_UeuyK6odwpGKAF17z6htswR59k81&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=OOVhr9x8pQZSCZOvV8FoP81RKilgqfPk0Qc4wtkkpcgNuAw63Zyuq8F2dz5Fmo2mOG7LicTSXHWqstltPYG2e17cUmE4PyXwuaJI9KNzJIxblHxLZuGq0juwrQeJbA7iHVuNGdhwGxi76QAqzqv_2mA-sgg1&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=6c27UNCriIYVQBhHf92QOOh2M-A_T0UTNwb_6wB00tIkJWqX-Lik3g14CA8SSPkWpbV96yPQn5rHnnhYEwEJl8IMvNiaSSZAKQFc4_6QhteV1PVCi9wOPMPArGohLaa51kV3Nq7Lfj-R2NjHKl1a_h-dIYE1&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=zdIlHK-bNTNfj8f9RMFmYA0JlnIdXON-0NsvMHsYQr_meYaPvEXgE_RLLfi42-TwYyF_oAZVjs-fp0AyOg2Cy_KwZImXk6lVaJzn289HOCw9IEdvxN2k70pHeGjw5HRGmFWXlSPKMeBIFpZUez4eOS5iy541&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=Q7-Fy7F_RB3NF8QFux3otxpsoNJz6XrcZsbkRc4ze13WIAI9XETXJ-8K1YUgGnQ50gS6fn8O5LsIquo1TxsBFqRdUbk-_x74CWtHw5qJBzVuPaQfyvFsJkcMaby5NuZVwun0Fu5RYTfvqdsvshNKK4jV83U1&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=IKO34KYjAKiwuSMMx10O2MTf2SXvfW9miAqKOluafO62NnXPXLOQ1vznkGV7PhtNKdLyh4ZjesWqlv3dSnZ5fZ9Oia3WVuNsyQE2yLewMWPGM9wabh1b2__nnQTjpA685jXXF9LCcu_9PRWc11AiU_TDLUg1&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=Z9sNw3HNMXNl1-W8ll1opsG0M3_Vp4DMuF5X-gYe0WNE3R2_sVpmC9GAxtI2THluPzKt-rzCj6eE2KCC7jCOyn1DF0wmOWXb8F36wSMjaV5tSgIFbkvDLvKFK_-0TnrYGDW0EBrn5u2JnUOB0HQSY2oAo9Q1&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/ScriptResource.axd?d=dDodMehax8a8evuQ8V3R5hRUoRSE3WfCMu4XjYlkd1K8l6lPAnZAfc8_c_oYg4AiOTkhDvC1KjVf2qV2qu5aeZfMHFG1op9fNUCZfED3-RLeLVhJ-WIZkV9G1VelpGXNJAGhhmY5tMw9bjga0o1EZ0k9V6-36zPnhK1igbJsZYs9KF9d0&t=ffffffffe6d5c1dc",
    "https://udyamregistration.gov.in/assets/img/MINISTRY_NAME.png",
    "https://udyamregistration.gov.in/bootstrap4/plugins/fontawesome-free/css/all.min.css",
    "https://udyamregistration.gov.in/assets/css/pageoverlay.css",
    "https://udyamregistration.gov.in/bootstrap4/dist/css/adminlte.min.css",
    "https://udyamregistration.gov.in/js/datecheck.js",
    "https://fonts.gstatic.com/s/opensans/v43/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-muw.woff2",
    "https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2",
    "https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfecg.woff2",
    "https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2",
    "https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2",
    "https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2",
    "https://udyamregistration.gov.in/bootstrap4/plugins/jquery/jquery.min.js",
    "https://www.google-analytics.com/mp/collect?measurement_id=G-03XW3FWG7L&api_secret=Px06eCtvQLS0hVSB2MPj_g",
    "https://udyamregistration.gov.in/Images/delete.jpg",
    "https://ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js",
    "https://udyamregistration.gov.in/js/jquery-1.9.1.min.js",
    "https://udyamregistration.gov.in/Udyam_User/js/jquery-ui.css",
    "https://udyamregistration.gov.in/js/jquery-ui.js",
    "https://udyamregistration.gov.in/assets/vendor/jquery/jquery.min.js",
    "https://udyamregistration.gov.in/videos/udyam.png",
    "https://udyamregistration.gov.in/assets/vendor/bootstrap/js/bootstrap.bundle.min.js",
    "https://udyamregistration.gov.in/assets/vendor/jquery.easing/jquery.easing.min.js",
    "https://udyamregistration.gov.in/assets/vendor/php-email-form/validate.js",
    "https://udyamregistration.gov.in/assets/vendor/counterup/counterup.min.js",
    "https://udyamregistration.gov.in/assets/vendor/isotope-layout/isotope.pkgd.min.js",
    "https://udyamregistration.gov.in/assets/vendor/owl.carousel/owl.carousel.min.js",
    "https://udyamregistration.gov.in/assets/vendor/waypoints/jquery.waypoints.min.js",
    "https://udyamregistration.gov.in/assets/vendor/venobox/venobox.min.js",
    "https://udyamregistration.gov.in/assets/js/main.js",
    "https://udyamregistration.gov.in/assets/vendor/aos/aos.js",
    "https://cdn.ux4g.gov.in/accessibility-beta-v1.14/accessibility-widget.js",
    "https://udyamregistration.gov.in/Images/delete.jpg",
    "https://udyamregistration.gov.in/bootstrap4/plugins/fontawesome-free/webfonts/fa-solid-900.woff2",
    "https://udyamregistration.gov.in/Udyam_User/js/jquery-ui.css",
    "https://udyamregistration.gov.in/assets/img/hero-bg.jpg",
    "https://udyamregistration.gov.in/assets/vendor/boxicons/fonts/boxicons.woff2",
    "https://cdn.ux4g.gov.in/accessibility-beta-v1.15/accessibility-widget.css",
    "https://www.ux4g.gov.in/assets/img/logo/ux4g-logo.svg",
    "https://udyamregistration.gov.in/assets/vendor/icofont/fonts/icofont.woff2",
    "https://udyamregistration.gov.in/WebResource.axd?d=SfEgFM-2eBT0bsr1gq3wf0_d_fABD7PbnT9bajv6itRGlg08xSroiQS6z9d2sbj3MzDoIarW20OU2MCGO1YMYs3-q7WSBdXfRsmoWI0vwfx7droJoyWUj6DmhGwCoV7LK4IH4gxkl7UHUWj1peWcmzfqTuw1&t=637110559240000000",
    "https://udyamregistration.gov.in/WebResource.axd?d=ULkEDsNrH7O-A_WoVrRzvc_tvAkJwe-xOhidY1Hn8HTnD8btpqk8pdSPyLU_nu02ypp7uWH11seBjX0Lt3cfLXtwE5Au0orMCGXqbWw7O-12gmPSFHlmJRuIn7-faFujHxm47Ia3m9BUF8lTNc2vuq1P0Lg1&t=637110559240000000"
];

const delay = ms => new Promise(res => setTimeout(res, ms));

(async () => {
  for (const url of urls) {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const filename = url.split('/').pop().split('?')[0] || 'file';

      const a = document.createElement('a');
      a.href = URL.createObjectURL(blob);
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      a.remove();

      console.log(`✔ Downloaded: ${filename}`);
      await delay(500); // avoid overwhelming the browser
    } catch (err) {
      console.warn(`✘ Failed: ${url}`, err);
    }
  }
})();
