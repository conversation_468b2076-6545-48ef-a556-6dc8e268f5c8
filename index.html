<html xmlns="http://www.w3.org/1999/xhtml" webcrx=""><head id="ctl00_Head1"><meta charset="utf-8"><meta content="width=device-width, initial-scale=1.0" name="viewport"><title>
	Update Udyam Registration Data
</title><link href="../assets/img/favicon.png" rel="icon"><link href="../assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Roboto:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet"><link href="../assets/vendor/icofont/icofont.min.css" rel="stylesheet"><link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet"><link href="../assets/vendor/owl.carousel/assets/owl.carousel.min.css" rel="stylesheet"><link href="../assets/vendor/venobox/venobox.css" rel="stylesheet"><link href="../assets/vendor/aos/aos.css" rel="stylesheet"><link href="../assets/css/style.css" rel="stylesheet">
   <script type="text/javascript">
       window.history.forward();
       function noBack() { window.history.forward(); }
    </script><script src="blob:https://udyamregistration.gov.in/22aaea2a-adb0-4ad5-983c-fe55f245011c"></script> 
<link href="/WebResource.axd?d=HssZ55r-YQGVRqZIygPpAA8-mSvFISHNH64Mhogg7NHdWbEzTKZSjlDiptGdMvAWB9eRPLKFT9JuSF3bNH-Qg3Q1zhmHD_W7wwyCfsh4rdTjgSaA-yVMXvIZrhBB3yAL9T4OR2r7m1kcJpPUtdk3MIolK1w1&amp;t=637110559240000000" type="text/css" rel="stylesheet"></head>
<body data-aos-easing="ease" data-aos-duration="1000" data-aos-delay="0" cz-shortcut-listen="true"><button type="button" class="mobile-nav-toggle d-lg-none" style="zoom: 1; word-spacing: 0em;"><i class="icofont-navigation-menu" style="word-spacing: 0em;"></i></button>
    <form name="aspnetForm" method="post" action="./Udyam_UpdateNew.aspx" onsubmit="javascript:return WebForm_OnSubmit();" id="aspnetForm" style="zoom: 1; word-spacing: 0em;">
<div style="word-spacing: 0em;">
<input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET" value="" style="word-spacing: 0em;">
<input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT" value="" style="word-spacing: 0em;">
<input type="hidden" name="__LASTFOCUS" id="__LASTFOCUS" value="" style="word-spacing: 0em;">
<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="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" style="word-spacing: 0em;">
</div>

<script type="text/javascript" style="word-spacing: 0em;">
//<![CDATA[
var theForm = document.forms['aspnetForm'];
if (!theForm) {
    theForm = document.aspnetForm;
}
function __doPostBack(eventTarget, eventArgument) {
    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
        theForm.__EVENTTARGET.value = eventTarget;
        theForm.__EVENTARGUMENT.value = eventArgument;
        theForm.submit();
    }
}
//]]>
</script>


<script src="/WebResource.axd?d=BayMCezD8XvrvgveJL7NURf1ZwggoPVyM7Azy9RELEDeozEDadElOKK1OmtKleKTbkwC5MxyRR4izP-NhA9-m7Rjzn41&amp;t=638568802371266408" type="text/javascript" style="word-spacing: 0em;"></script>


<script src="/ScriptResource.axd?d=ZRl_HRH7Wke0zmP7SmEl0sZYQXajrBJXxRRKjecVYNtNYMy970nsJzRLuzMB4em4VfayNHJ4H9CGAykq7nZ0wW8jveyMIDdfhyPPp_mKh1BNWQNSyuGxRH5Fx3QYOGkZ9MLAp5iOSpeNH9WYOQHe5Mey8LM1&amp;t=ffffffffba22f784" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=ZcC4pqWDloVZ_h8bxX2LIa6VQrRMImWwX4COhUwztNeZe0XU-5LdMBdPxmX1UTsqEeR-38ISjw-vdHM5kxCpdBlY8JbMtCzMhDu0G5Rym63s71eJwKRF7yrPYppaKBwNjGOMEYxxr4TTPz8k7ZUPGmhG_wI1&amp;t=ffffffffc820c398" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=H5h_4QA61SxxXAxj511af0BnAMT5qO8EtoCzNK83omYo7L8yhx8VocAT7EDx7BnQ9L7N3e7_rZ-d30pMADP_Me13UYW9_O6GP4b7BobwB3zLIgfuVz_DJtW2_wVymSvo6RDpfnk79DQcVRxskdIpc-62wPR0x0QxCuRkvcszaFWsHFVo0&amp;t=ffffffffc820c398" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=SZAF229YE1BzUkr8dWnGQ6frmjznn1s--Ayb928-6iH4oYBE-Qy3B5qaj76OOAIXHRT9xclKyJkAnJ8zbBT0o0xv9NfEZoQU4OfZAmAJCmzIkBdm1iwpgBtjjsyvGLlq_uchjjXsDeykbOHzgRR7SMhYeXo1&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=4WMj220ly8_LUuALPAEmhGazPiiGtzj_Fizqr8BSkaDvfljcR_Z4WWRNq_NlZGCwJ9hvvlnquoe9-zmDIBA9ukY-pHR0i3nKuv__iIgT8qVcVUdseCNVwKYVLQ4dUUe3kykDp5rSkgz_xDMvvo7rDa_OFjc1&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=leAbOU-Oohss4A1vmGoupOz81ye78rpVL51k4dII8hYcU9OAEfMTUsf-LaI1WH23plSxyCNkZUrDY0xqGrAxxyKmjfLAbt8x7KVj2bYPOgUt6KjMde2ukT0xsWqiMSvz0yITN98BdktmGLbR715vjaaaSWhvyAtOlYPURCThDu1IPZwR0&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=sO_jTt2J_khP0ep7ou03lyGVw-dYCRtGwUaJKH1rUctx77jMO4_o9FnpsztVAnwg7vLkd3TjGHNZa_TdIImK666iynkeHN_8dTpNVuhbDQhPD4t0g-m4LyeStIX8poe_UeuyK6odwpGKAF17z6htswR59k81&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=OOVhr9x8pQZSCZOvV8FoP81RKilgqfPk0Qc4wtkkpcgNuAw63Zyuq8F2dz5Fmo2mOG7LicTSXHWqstltPYG2e17cUmE4PyXwuaJI9KNzJIxblHxLZuGq0juwrQeJbA7iHVuNGdhwGxi76QAqzqv_2mA-sgg1&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=6c27UNCriIYVQBhHf92QOOh2M-A_T0UTNwb_6wB00tIkJWqX-Lik3g14CA8SSPkWpbV96yPQn5rHnnhYEwEJl8IMvNiaSSZAKQFc4_6QhteV1PVCi9wOPMPArGohLaa51kV3Nq7Lfj-R2NjHKl1a_h-dIYE1&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=zdIlHK-bNTNfj8f9RMFmYA0JlnIdXON-0NsvMHsYQr_meYaPvEXgE_RLLfi42-TwYyF_oAZVjs-fp0AyOg2Cy_KwZImXk6lVaJzn289HOCw9IEdvxN2k70pHeGjw5HRGmFWXlSPKMeBIFpZUez4eOS5iy541&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=Q7-Fy7F_RB3NF8QFux3otxpsoNJz6XrcZsbkRc4ze13WIAI9XETXJ-8K1YUgGnQ50gS6fn8O5LsIquo1TxsBFqRdUbk-_x74CWtHw5qJBzVuPaQfyvFsJkcMaby5NuZVwun0Fu5RYTfvqdsvshNKK4jV83U1&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=IKO34KYjAKiwuSMMx10O2MTf2SXvfW9miAqKOluafO62NnXPXLOQ1vznkGV7PhtNKdLyh4ZjesWqlv3dSnZ5fZ9Oia3WVuNsyQE2yLewMWPGM9wabh1b2__nnQTjpA685jXXF9LCcu_9PRWc11AiU_TDLUg1&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=Z9sNw3HNMXNl1-W8ll1opsG0M3_Vp4DMuF5X-gYe0WNE3R2_sVpmC9GAxtI2THluPzKt-rzCj6eE2KCC7jCOyn1DF0wmOWXb8F36wSMjaV5tSgIFbkvDLvKFK_-0TnrYGDW0EBrn5u2JnUOB0HQSY2oAo9Q1&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script src="/ScriptResource.axd?d=dDodMehax8a8evuQ8V3R5hRUoRSE3WfCMu4XjYlkd1K8l6lPAnZAfc8_c_oYg4AiOTkhDvC1KjVf2qV2qu5aeZfMHFG1op9fNUCZfED3-RLeLVhJ-WIZkV9G1VelpGXNJAGhhmY5tMw9bjga0o1EZ0k9V6-36zPnhK1igbJsZYs9KF9d0&amp;t=ffffffffe6d5c1dc" type="text/javascript" style="word-spacing: 0em;"></script>
<script type="text/javascript" style="word-spacing: 0em;">
//<![CDATA[
function WebForm_OnSubmit() {
if (typeof(ValidatorOnSubmit) == "function" && ValidatorOnSubmit() == false) return false;
return true;
}
//]]>
</script>

<div style="word-spacing: 0em;">

	<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="0B0E9C31" style="word-spacing: 0em;">
	<input type="hidden" name="__VIEWSTATEENCRYPTED" id="__VIEWSTATEENCRYPTED" value="" style="word-spacing: 0em;">
	<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="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" style="word-spacing: 0em;">
</div>
     <script type="text/javascript" style="word-spacing: 0em;">
//<![CDATA[
Sys.WebForms.PageRequestManager._initialize('ctl00$sm', 'aspnetForm', ['tctl00$ContentPlaceHolder1$UpdatePaneldd1',''], [], [], 90, 'ctl00');
//]]>
</script>

     <!-- ======= Header ======= -->
  <header id="header" class="fixed-top header-inner-pages header-scrolled" style="word-spacing: 0em;">
     

<div class="container-fluid" style="word-spacing: 0em;">
    <div class="row justify-content-center" style="word-spacing: 0em;">
        <div class="col-xl-10 d-flex align-items-center" style="word-spacing: 0em;">
            <h1 class="logo mr-auto" style="word-spacing: 0em;"><a href="udyam_Dashboard.aspx" style="word-spacing: 0em;">
                <img src="../assets/img/MINISTRY_NAME.png" width="300" style="word-spacing: 0em;"></a></h1>
            <nav class="nav-menu d-none d-lg-block" style="word-spacing: 0em;">
                <ul style="word-spacing: 0em;">
                    <li class="" style="word-spacing: 0em;"><a href="udyam_Dashboard.aspx" style="word-spacing: 0em;">Home</a></li>
                    
                    <li style="word-spacing: 0em;">
                        
                    </li>
                    <li style="word-spacing: 0em;"><a href="Udyam_Investment_TurnoverDetail.aspx" style="word-spacing: 0em;">View Classification Details</a></li>
                   
                    <li style="word-spacing: 0em;"><a href="Udyam_Cancellation.aspx" style="word-spacing: 0em;">Cancellation</a></li>
                    
                    <li style="word-spacing: 0em;"><a href="../Udyam_Logout.aspx?type=udyam" style="word-spacing: 0em;">Logout</a></li>
                </ul>
            </nav>
        </div>
    </div>

</div>

  </header><!-- End Header -->
    <div style="word-spacing: 0em;">
        
    <script type="text/javascript" style="word-spacing: 0em;">
        function Call_Map() {
            window.open('../latlongtest.aspx?id=' + document.getElementById('ctl00_ContentPlaceHolder1_hdndtcode').value, '_blank', 'width=900,height=600, scrollbars=no,resizable=no');
         }
    </script>
    <link rel="stylesheet" href="../bootstrap4/plugins/fontawesome-free/css/all.min.css" style="word-spacing: 0em;">
    <link rel="stylesheet" href="../assets/css/pageoverlay.css" style="word-spacing: 0em;">
   
    <link rel="stylesheet" href="../bootstrap4/dist/css/adminlte.min.css" style="word-spacing: 0em;">
   
    
    <script src="../js/datecheck.js" type="text/javascript" style="word-spacing: 0em;"></script>
    <script src="../bootstrap4/plugins/jquery/jquery.min.js" style="word-spacing: 0em;"></script>

    <script language="javascript" type="text/javascript" style="word-spacing: 0em;">
        function masking(input, textbox) {
            if (input.length == 4 || input.length == 9) {
                input = input + '-';
                textbox.value = input;
            }
        }
    </script>

    <script type="text/javascript" language="javascript" style="word-spacing: 0em;">
        function DepCostWord() {
            var tValue = document.getElementById('ctl00_ContentPlaceHolder1_txtDepCost').value;
            if (tValue.length != 0) {
                var finalWord = price_in_words(tValue);
                document.getElementById('spanDepreciatedCost').innerHTML = '';
                document.getElementById('spanDepreciatedCost').innerHTML = finalWord;
            }
            else { document.getElementById('spanDepreciatedCost').innerHTML = ''; }
        }

        function ExCostWord() {
            var tValue = document.getElementById('ctl00_ContentPlaceHolder1_txtExCost').value;
            if (tValue.length != 0) {
                var finalWord = price_in_words(tValue);
                document.getElementById('spanExclusionCost').innerHTML = '';
                document.getElementById('spanExclusionCost').innerHTML = finalWord;
            }
            else { document.getElementById('spanExclusionCost').innerHTML = ''; }
        }

        function ExTurnoverAWord() {
            var tValue = document.getElementById('ctl00_ContentPlaceHolder1_txtTotalTurnoverA').value;
            if (tValue.length != 0) {
                var finalWord = price_in_words(tValue);
                document.getElementById('spanTotalTurnoverA').innerHTML = '';
                document.getElementById('spanTotalTurnoverA').innerHTML = finalWord;
            }
            else { document.getElementById('spanTotalTurnoverA').innerHTML = ''; }
        }

        function ExTurnoverBWord() {
            var tValue = document.getElementById('ctl00_ContentPlaceHolder1_txtTotalTurnoverB').value;
            if (tValue.length != 0) {
                var finalWord = price_in_words(tValue);
                document.getElementById('spanTotalTurnoverB').innerHTML = '';
                document.getElementById('spanTotalTurnoverB').innerHTML = finalWord;
            }
            else { document.getElementById('spanTotalTurnoverB').innerHTML = ''; }
        }

        function price_in_words(price) {
            var sglDigit = ["Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"],
                dblDigit = ["Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"],
                tensPlace = ["", "Ten", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"],
                handle_tens = function (dgt, prevDgt) {
                    return 0 == dgt ? "" : " " + (1 == dgt ? dblDigit[prevDgt] : tensPlace[dgt])
                },
                handle_utlc = function (dgt, nxtDgt, denom) {
                    return (0 != dgt && 1 != nxtDgt ? " " + sglDigit[dgt] : "") + (0 != nxtDgt || dgt > 0 ? " " + denom : "")
                };

            var str = "",
                digitIdx = 0,
                digit = 0,
                nxtDigit = 0,
                words = [];
            if (price += "", isNaN(parseInt(price))) str = "";
            else if (parseInt(price) > 0 && price.length <= 10) {
                for (digitIdx = price.length - 1; digitIdx >= 0; digitIdx--) switch (digit = price[digitIdx] - 0, nxtDigit = digitIdx > 0 ? price[digitIdx - 1] - 0 : 0, price.length - digitIdx - 1) {
                    case 0:
                        words.push(handle_utlc(digit, nxtDigit, ""));
                        break;
                    case 1:
                        words.push(handle_tens(digit, price[digitIdx + 1]));
                        break;
                    case 2:
                        words.push(0 != digit ? " " + sglDigit[digit] + " Hundred" + (0 != price[digitIdx + 1] && 0 != price[digitIdx + 2] ? " and" : "") : "");
                        break;
                    case 3:
                        words.push(handle_utlc(digit, nxtDigit, "Thousand"));
                        break;
                    case 4:
                        words.push(handle_tens(digit, price[digitIdx + 1]));
                        break;
                    case 5:
                        words.push(handle_utlc(digit, nxtDigit, "Lakh"));
                        break;
                    case 6:
                        words.push(handle_tens(digit, price[digitIdx + 1]));
                        break;
                    case 7:
                        words.push(handle_utlc(digit, nxtDigit, "Crore"));
                        break;
                    case 8:
                        words.push(handle_tens(digit, price[digitIdx + 1]));
                        break;
                    case 9:
                        words.push(0 != digit ? " " + sglDigit[digit] + " Hundred" + (0 != price[digitIdx + 1] || 0 != price[digitIdx + 2] ? " and" : " Crore") : "")
                }
                str = words.reverse().join("")
            } else str = "";
            return str
        }

        function ValidateDate(osrc, args) {
            var dt = args.Value
            if (isDate(dt) == false) {

                args.IsValid = false;
                return false
            }
            else {
                args.IsValid = true;
                return true
            }
        }

        function toASCII(source, args) {
            var r1 = 0, r2 = 0;
            for (var i = 0; i < args.Value.length; i++) {
                r = args.Value.charCodeAt(i);
                if ((r >= 32 && r <= 127) || r == 8 || r == 9 || r == 13 || r == 10) {
                    r1 = 1;
                }
                else {
                    r2 = 1;
                    break;
                }
            }
            if (r2 > 0) {
                args.IsValid = false;
                return false;
            }
            else {
                args.IsValid = true;
                return true;
            }
        }
        function showDate(sender, args) {
            if (sender._textbox.get_element().value == "") {
                var todayDate = new Date();
                sender._selectedDate = new Date(todayDate.setFullYear(todayDate.getFullYear()));
            }
        }

        function ChngDateFormat(dtStr) {
            var dtCh = "/";
            var pos1 = dtStr.indexOf(dtCh)
            var pos2 = dtStr.indexOf(dtCh, pos1 + 1)
            var strMonth = dtStr.substring(pos1 + 1, pos2)
            var strDay = dtStr.substring(0, pos1)
            var strYear = dtStr.substring(pos2 + 1)
            dtStr = strMonth + '/' + strDay + '/' + strYear;
            return dtStr;
        }

        function calc_dateIncopold(txt1) {
            var txtdateIncorporation = new Date(ChngDateFormat(document.getElementById('ctl00_ContentPlaceHolder1_txtdateIncorporation').value));
          var today = new Date();
          var dd = today.getDate();
          var mm = today.getMonth() + 1;
          var yyyy = today.getFullYear();
          if (dd < 10) {
              dd = '0' + dd;
          }
          if (mm < 10) {
              mm = '0' + mm;
          }
          var today = new Date(mm + '/' + dd + '/' + yyyy);
          if (txtdateIncorporation >= today) {
              alert('You can not select current & future Date ');
              document.getElementById(txt1).value = '';
              //  document.getElementById(txt1).focus();
              return false;
          }
          return true;
      }

      function calc_dateIncop(txt1) {

          var txtdateIncorporation = new Date(ChngDateFormat(document.getElementById('ctl00_ContentPlaceHolder1_txtdateIncorporation').value));
            var txtDOR = new Date(ChngDateFormat(document.getElementById('ctl00_ContentPlaceHolder1_hdnDoR').value));
            if (txtdateIncorporation >= txtDOR) {
                alert('Date of Incorporation can not be greater than Date of Registration ');
                document.getElementById(txt1).value = '';
                //  document.getElementById(txt1).focus();
                return false;
            }
            return true;
        }
        function calc_dateComes(txt1) {
            var txtcommencedate = new Date(ChngDateFormat(document.getElementById('ctl00_ContentPlaceHolder1_txtcommencedate').value));
            var txtdateIncorporation = new Date(ChngDateFormat(document.getElementById('ctl00_ContentPlaceHolder1_txtdateIncorporation').value));
            var today = new Date();
            var dd = today.getDate();
            var mm = today.getMonth() + 1;
            var yyyy = today.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            var today = new Date(mm + '/' + dd + '/' + yyyy);
            if (txtcommencedate >= today) {
                alert('You can not select current & future Date ');
                document.getElementById(txt1).value = '';
                // document.getElementById(txt1).focus();
                return false;
            }
            if (txtdateIncorporation > txtcommencedate) {
                alert('Date of Commencment is not less than Date of Incorporation ');
                document.getElementById(txt1).value = '';
                // document.getElementById(txt1).focus();
                return false;
            }
            return true;
        }


        function calc_dateDob(txt1) {
            var txtdob = new Date(ChngDateFormat(document.getElementById('ctl00_ContentPlaceHolder1_txtdob').value));
            var today = new Date();
            var dd = today.getDate();
            var mm = today.getMonth() + 1;
            var yyyy = today.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            var today = new Date(mm + '/' + dd + '/' + yyyy);
            if (txtdob > today) {
                alert('You can not select future Date ');
                document.getElementById(txt1).value = '';
                // document.getElementById(txt1).focus();
                return false;
            }
            return true;
        }
    </script>
    <script type="text/javascript" language="javascript" style="word-spacing: 0em;">

        $(document).ready(function () {

            $('.Csstxt1').keypress(function () {
                if ($(this).val().length == 4)
                    $('.Csstxt2').focus();
            });
            $('.Csstxt2').keypress(function () {
                if ($(this).val().length == 4)
                    $('.Csstxt3').focus();
            });
            $('.Csstxt3').keypress(function () {
                if ($(this).val().length == 4)
                    $('.Csstxt4').focus();
            });
        });

    </script>
    <script language="javascript" type="text/javascript" style="word-spacing: 0em;">
        var id = 0;
        function CaptchaRefresh(img, src) {

            id++;
            document.getElementById(img).src = src + "?id=" + id;

        }
    </script>
    <script type="text/javascript" style="word-spacing: 0em;">
        function checkDate(sender, args) {
            if (sender._selectedDate > new Date()) {
                alert("You cannot select future date!");
                //sender._selectedDate = new Date();
                // set the date back to the current date ....
                document.getElementById("ctl00_ContentPlaceHolder1_txtcommencedate").value = ""
                document.getElementById('').value = "";
                //you can put the if condition for sunday's
                sender._textbox.set_Value(sender._selectedDate.format(sender._format))
            }
        }
        function checkIncorporationDate(sender, args) {
            if (sender._selectedDate > new Date()) {
                alert("You cannot select future date!");
                //sender._selectedDate = new Date();
                // set the date back to the current date ....
                document.getElementById("ctl00_ContentPlaceHolder1_txtdateIncorporation").value = ""
                document.getElementById('').value = "";
                //you can put the if condition for sunday's
                sender._textbox.set_Value(sender._selectedDate.format(sender._format))
            }
        }
    </script>

    <script type="text/javascript" style="word-spacing: 0em;">
        function resetValidationState() {
            window.Page_BlockSubmit = false;
        }
        function CountGridRowActivity(source, args) {
            var gvRegProduct = document.getElementById('ctl00_ContentPlaceHolder1_hdnRegProduct').value;
            if (gvRegProduct == 0) {
                args.IsValid = false;
                return false;
            }
            else {
                args.IsValid = true;
                return true;
            }
        }

        function CountGridRowPlant(source, args) {
            var gvRegPlant = document.getElementById('ctl00_ContentPlaceHolder1_hdnRegPlant').value;
            if (gvRegPlant == 0) {
                args.IsValid = false;
                return false;
            }
            else {
                args.IsValid = true;
                return true;
            }
        }
        function CaculateEmp() {
            var personMale = document.getElementById('ctl00_ContentPlaceHolder1_txtNoofpersonMale').value;
            var personFemale = document.getElementById('ctl00_ContentPlaceHolder1_txtNoofpersonFemale').value;
            var personOthers = document.getElementById('ctl00_ContentPlaceHolder1_txtNoofpersonOthers').value;
            if (personMale == "")
                personMale = 0;
            if (personFemale == "")
                personFemale = 0;
            if (personOthers == "")
                personOthers = 0;
            var result = parseInt(personMale) + parseInt(personFemale) + parseInt(personOthers);
            if (!isNaN(result)) {
                document.getElementById('ctl00_ContentPlaceHolder1_txttotalemp').value = result;
            }
        }

        function ValidateTurnover(source, args) {
            var hdnWhetherPan = document.getElementById('ctl00_ContentPlaceHolder1_hdnWhetherPan').value;
            var rbpanyesno = document.getElementById("ctl00_ContentPlaceHolder1_rbpanyesno").getElementsByTagName("input");
            if (rbpanyesno[0].checked && hdnWhetherPan == 2) {
                var chkLista = document.getElementById("ctl00_ContentPlaceHolder1_rblWhetherGstn").getElementsByTagName("input");
                var TTurnoverA = document.getElementById("ctl00_ContentPlaceHolder1_txtTotalTurnoverA").value;
                if (TTurnoverA > 4000000 && chkLista[1].checked) {
                    args.IsValid = false;
                    return false;
                }
                else {
                    args.IsValid = true;
                    return true;
                }
            }
        }

        function CaculateTurnover() {
            var TTurnoverA = document.getElementById('ctl00_ContentPlaceHolder1_txtTotalTurnoverA').value;
            var TTurnoverB = document.getElementById('ctl00_ContentPlaceHolder1_txtTotalTurnoverB').value;

            if (TTurnoverA == "")
                TTurnoverA = 0;
            if (TTurnoverB == "")
                TTurnoverB = 0;

            var result = parseInt(TTurnoverA) - parseInt(TTurnoverB);
            if (!isNaN(result)) {
                if (result < 0) {
                    document.getElementById('ctl00_ContentPlaceHolder1_txtNetTurnover').value = 0;
                }
                else
                    document.getElementById('ctl00_ContentPlaceHolder1_txtNetTurnover').value = result;
            }
        }

        function CaculateInvestment() {
            var TDepA = document.getElementById('ctl00_ContentPlaceHolder1_txtDepCost').value;
            var TExB = document.getElementById('ctl00_ContentPlaceHolder1_txtExCost').value;

            if (TDepA == "")
                TDepA = 0;
            if (TExB == "")
                TExB = 0;

            var result = parseInt(TDepA) - parseInt(TExB);
            if (!isNaN(result)) {
                if (parseInt(TDepA) < parseInt(TExB)) {
                    document.getElementById('ctl00_ContentPlaceHolder1_txtNetInvestmentcost').value = 0;
                } else {

                    document.getElementById('ctl00_ContentPlaceHolder1_txtNetInvestmentcost').value = result;
                }
            }
        }
    </script>
    <script type="text/javascript" style="word-spacing: 0em;">

        function ConfirmSubmitfinal() {
            var flag = Page_ClientValidate('Y');
            if (flag)
                flag = Page_ClientValidate('Z');

            if (flag) {
                if (Page_IsValid) {
                    return confirm('Are you sure that you have entered correct data?');
                }
            }
            return Page_IsValid;
        }
    </script>
    <script type="text/javascript" style="word-spacing: 0em;">
        function HideModalPopup() {
            $find("mpepass").hide();

            return false;
        }
        function showModalPopup() {
            $find("mpepass").show();
            return false;
        }
        function HideModalPopup1() {
            $find("mpepass1").hide();
            return false;
        }
        function showModalPopup1() {
            $find("mpepass1").show();
            return false;
        }
    </script>
    <script type="text/javascript" style="word-spacing: 0em;">
        function ClientValidatePan(source, arguments) {

            var txt = document.getElementById('ctl00_ContentPlaceHolder1_txtPan');
            var ddl = document.getElementById('ctl00_ContentPlaceHolder1_ddlTypeofOrg');
            var decision = ddl.value;
            if (decision == "2" || decision == "3" || decision == "4" || decision == "5" || decision == "6" || decision == "7" || decision == "8" || decision == "9" || decision == "10" || decision == "11") {
                if (txt.value == '') {
                    arguments.IsValid = false;
                    return false;
                }
                else {
                    arguments.IsValid = true;
                    return true;
                }
            } else {
                arguments.IsValid = true;
                return true;
            }
        }
    </script>
    <script type="text/javascript" style="word-spacing: 0em;">

        function checkAgreementP(source, args) {
            var chkAgree = document.getElementById('ctl00_ContentPlaceHolder1_chkDecarationP');
            if (chkAgree.checked) {
                args.IsValid = true;
            }
            else {
                args.IsValid = false;
            }
        }

    </script>
    <style type="text/css" style="word-spacing: 0em;">
        /*Calendar Control CSS*/
        .cal_Theme1 .ajax__calendar_container {
            background-color: #DEF1F4;
            border: solid 1px #77D5F7;
            width: 185px;
        }

        .cal_Theme1 .ajax__calendar_header {
            background-color: #ffffff;
            margin-bottom: 4px;
        }

        .cal_Theme1 .ajax__calendar_title,
        .cal_Theme1 .ajax__calendar_next,
        .cal_Theme1 .ajax__calendar_prev {
            color: #004080;
            padding-top: 3px;
        }

        .cal_Theme1 .ajax__calendar_body {
            background-color: #ffffff;
            border: solid 1px #77D5F7;
        }

        .cal_Theme1 .ajax__calendar_dayname {
            text-align: left;
            margin-bottom: 4px;
            margin-top: 2px;
            color: #004080;
            height: 10px;
            width: 22px;
        }

        .cal_Theme1 .ajax__calendar_day {
            color: #004080;
            text-align: center;
        }

        .cal_Theme1 .ajax__calendar_hover .ajax__calendar_day,
        .cal_Theme1 .ajax__calendar_hover .ajax__calendar_month,
        .cal_Theme1 .ajax__calendar_hover .ajax__calendar_year,
        .cal_Theme1 .ajax__calendar_active {
            color: #004080;
            font-weight: bold;
            background-color: #DEF1F4;
        }


        .cal_Theme1 .ajax__calendar_today {
            font-weight: bold;
            height: 20px;
        }

        .cal_Theme1 .ajax__calendar_other,
        .cal_Theme1 .ajax__calendar_hover .ajax__calendar_today,
        .cal_Theme1 .ajax__calendar_hover .ajax__calendar_title {
            color: #bbbbbb;
        }
    </style>
    <style type="text/css" style="word-spacing: 0em;">
        input[type="radio"] {
            margin: 0 2px 0 15px;
        }
    </style>

    <style type="text/css" style="word-spacing: 0em;">
        .AutoExtender {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11px;
            font-weight: normal;
            border: solid 1px #006699;
            line-height: 20px;
            padding: 10px;
            background-color: White;
            margin-left: 10px;
            z-index: 100;
        }


        .AutoExtenderList {
            border-bottom: dotted 1px #006699;
            cursor: pointer;
            color: Maroon;
        }

        .AutoExtenderHighlight {
            color: White;
            background-color: #006699;
            cursor: pointer;
        }

        #divwidth {
            width: 150px !important;
        }

            #divwidth div {
                width: 150px !important;
            }

        .PromptCSS {
            color: #000;
            font-size: large;
            font-style: italic;
            font-weight: bold;
            background-color: #FFF;
            border: solid 1px BurlyWood;
            height: 20px;
        }
    </style>
    <style style="word-spacing: 0em;">
        @keyframes blink {
            100% {
                color: red;
            }

            0% {
                color: blue;
            }
        }

        @-webkit-keyframes blink {
            100% {
                color: red;
            }

            0% {
                color: blue;
            }
        }

        .blink {
            -webkit-animation: blink 1s linear infinite;
            -moz-animation: blink 1s linear infinite;
            animation: blink 1s linear infinite;
        }
    </style>
    <style type="text/css" style="word-spacing: 0em;">
        .PopCal {
            z-index: 100;
        }
    </style>
    <style type="text/css" style="word-spacing: 0em;">
        .smallword {
            font-size: 11PX;
            color: green;
        }
    </style>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async="" src="https://www.googletagmanager.com/gtag/js?id=UA-166958537-1" style="word-spacing: 0em;"></script>
    <script style="word-spacing: 0em;">
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'UA-166958537-1');
    </script>

    <style type="text/css" style="word-spacing: 0em;">
        .ajax__calendar_container {
            z-index: 1000;
        }
    </style>
    <main id="main" style="word-spacing: 0em;">
        <div class="breadcrumbs" style="word-spacing: 0em;">
            <div class="container" style="word-spacing: 0em;">
                <div class="d-flex justify-content-between align-items-center" style="word-spacing: 0em;">
                    <p style="text-decoration: none; word-spacing: 0em;"><b style="word-spacing: 0em;">Welcome : </b>
                        <span id="ctl00_ContentPlaceHolder1_lblownername" style="word-spacing: 0em;">SHRI PRAVEEN KUMAR</span></p>
                    <ol style="word-spacing: 0em;">
                        <li style="word-spacing: 0em;"><a href="Udyam_Dashboard.aspx" style="word-spacing: 0em;">Home</a></li>
                        <li style="word-spacing: 0em;">Update Udyam Details</li>
                    </ol>
                </div>
            </div>
        </div>
        <section class="inner-page" style="word-spacing: 0em;">

            <div class="container" style="word-spacing: 0em;">
                <div id="ctl00_ContentPlaceHolder1_UpdatePaneldd1" style="word-spacing: 0em;">
	
                        <div id="ctl00_ContentPlaceHolder1_divpanmain" class="row" style="word-spacing: 0em;">
                            <div class="col-md-12" style="word-spacing: 0em;">
                                <!-- Input addon -->
                                <div class="card card-success" style="word-spacing: 0em;">
                                    <div class="card-header" style="word-spacing: 0em;">
                                        <h3 class="card-title" style="word-spacing: 0em;">PAN Verification</h3>
                                    </div>
                                    <div class="card-body" style="word-spacing: 0em;">

                                        
                                        <div class="row" style="word-spacing: 0em;">
                                            <div class="col-md-5" style="word-spacing: 0em;">
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label for="Ename" style="word-spacing: 0em;">
                                                        3. Type of Organisation <span class="smallword" style="word-spacing: 0em;">(Autofill)</span> / संगठन के प्रकार <span class="smallword" style="word-spacing: 0em;">(स्वत: भरण)</span>
                                                    </label>
                                                    <select name="ctl00$ContentPlaceHolder1$ddlTypeofOrg" id="ctl00_ContentPlaceHolder1_ddlTypeofOrg" class="form-control" onchange="resetValidationState(this);" style="word-spacing: 0em;">
		<option value="0" style="word-spacing: 0em;">Type of Organisation / संगठन के प्रकार</option>
		<option selected="selected" value="1" style="word-spacing: 0em;">1. Proprietary / एकल स्वामित्व</option>
		<option value="2" style="word-spacing: 0em;">2. Hindu Undivided Family / हिंदू अविभाजित परिवार (एचयूएफ)</option>
		<option value="3" style="word-spacing: 0em;">3. Partnership / पार्टनरशिप</option>
		<option value="4" style="word-spacing: 0em;">4. Co-Operative / सहकारी</option>
		<option value="5" style="word-spacing: 0em;">5. Private Limited Company / प्राइवेट लिमिटेड कंपनी</option>
		<option value="6" style="word-spacing: 0em;">6. Public Limited Company / पब्लिक लिमिटेड कंपनी</option>
		<option value="7" style="word-spacing: 0em;">7. Self Help Group / स्वयं सहायता समूह</option>
		<option value="9" style="word-spacing: 0em;">8. Limited Liability Partenership / सीमित दायित्व भागीदारी</option>
		<option value="10" style="word-spacing: 0em;">9. Society / सोसाईटी</option>
		<option value="11" style="word-spacing: 0em;">10. Trust / ट्रस्ट</option>
		<option value="8" style="word-spacing: 0em;">11. Others / अन्य</option>

	</select>
                                                    
                                                </div>
                                            </div>
                                            <div class="col-md-4" id="divpanyesno" style="word-spacing: 0em;">
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label style="word-spacing: 0em;">4. Do you have PAN / क्या आपके पास पैन है ?</label>
                                                    <table id="ctl00_ContentPlaceHolder1_rbpanyesno" disabled="disabled" onchange="resetValidationState(this);" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><span disabled="disabled" style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbpanyesno_0" type="radio" name="ctl00$ContentPlaceHolder1$rbpanyesno" value="1" checked="checked" disabled="disabled" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbpanyesno_0" style="word-spacing: 0em;">Yes / हाँ</label></span></td><td style="word-spacing: 0em;"><span disabled="disabled" style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbpanyesno_1" type="radio" name="ctl00$ContentPlaceHolder1$rbpanyesno" value="2" disabled="disabled" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rbpanyesno$1\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbpanyesno_1" style="word-spacing: 0em;">No / नहीं</label></span></td>
		</tr>
	</tbody></table>
                                                    <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator3" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                </div>
                                            </div>

                                            <div id="ctl00_ContentPlaceHolder1_divPANNumber" class="col-md-3" style="word-spacing: 0em;">
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label for="pancard" style="word-spacing: 0em;">
                                                        4.1 PAN / पैन <span class="smallword" style="word-spacing: 0em;"></span>
                                                    </label>
                                                    <input name="ctl00$ContentPlaceHolder1$txtPan" type="text" value="**********" maxlength="10" id="ctl00_ContentPlaceHolder1_txtPan" disabled="disabled" class="form-control" placeholder="Enter Pan Number" autocomplete="Off" style="text-transform: uppercase; word-spacing: 0em;">
                                                    <span id="ctl00_ContentPlaceHolder1_rfvpannumber" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    <span id="ctl00_ContentPlaceHolder1_RegularExpressionValidator5" style="color: red; display: none; word-spacing: 0em;">Invalid PAN Card</span>
                                                    
                                                    <span id="ctl00_ContentPlaceHolder1_lblPan" style="color: red; word-spacing: 0em;"></span>
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnPan" id="ctl00_ContentPlaceHolder1_hdnPan" value="**********" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnWhetherPan" id="ctl00_ContentPlaceHolder1_hdnWhetherPan" value="1" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnIsPanUpdated" id="ctl00_ContentPlaceHolder1_hdnIsPanUpdated" value="0" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnPan_AssessmentYear" id="ctl00_ContentPlaceHolder1_hdnPan_AssessmentYear" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnIS_UPDATED_FROM_PAN" id="ctl00_ContentPlaceHolder1_hdnIS_UPDATED_FROM_PAN" value="0" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnFYear" id="ctl00_ContentPlaceHolder1_hdnFYear" value="2023-24" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnIsClassified" id="ctl00_ContentPlaceHolder1_hdnIsClassified" value="1" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnDepreciatedCost" id="ctl00_ContentPlaceHolder1_hdnDepreciatedCost" value="0" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnExclusionCost" id="ctl00_ContentPlaceHolder1_hdnExclusionCost" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnTotalTurnoverA" id="ctl00_ContentPlaceHolder1_hdnTotalTurnoverA" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnExportTurnover" id="ctl00_ContentPlaceHolder1_hdnExportTurnover" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnWRITTEN_DOWN_VAL_LAST_DAY_PY" id="ctl00_ContentPlaceHolder1_hdnWRITTEN_DOWN_VAL_LAST_DAY_PY" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnTotalTurnover" id="ctl00_ContentPlaceHolder1_hdnTotalTurnover" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnItrType" id="ctl00_ContentPlaceHolder1_hdnItrType" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnASSESSMENT_YEAR" id="ctl00_ContentPlaceHolder1_hdnASSESSMENT_YEAR" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnIsgstnupdated" id="ctl00_ContentPlaceHolder1_hdnIsgstnupdated" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnEnterpriseName" id="ctl00_ContentPlaceHolder1_hdnEnterpriseName" value="PARVEEN KUMAR" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnEnterpriseType" id="ctl00_ContentPlaceHolder1_hdnEnterpriseType" value="A" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnCommencementDt" id="ctl00_ContentPlaceHolder1_hdnCommencementDt" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnUidToken" id="ctl00_ContentPlaceHolder1_hdnUidToken" style="word-spacing: 0em;">
                                                      
                                                     <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnDob" id="ctl00_ContentPlaceHolder1_hdnDob" style="word-spacing: 0em;">
                                                  <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnIsdob" id="ctl00_ContentPlaceHolder1_hdnIsdob" style="word-spacing: 0em;">
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnDoR" id="ctl00_ContentPlaceHolder1_hdnDoR" value="14/12/2023" style="word-spacing: 0em;">
                                                </div>
                                            </div>

                                        </div>
                                           

                                        <div class="row" style="word-spacing: 0em;">

                                            <div class="col-md-6" id="divdoI" style="display: none; word-spacing: 0em;">
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label style="word-spacing: 0em;">4.1.3 Do You Have DOB or DOI as per PAN/क्या आपके पास पैन के अनुसार जन्मतिथि या निगमन की तारीख है?</label>
                                                    <table id="ctl00_ContentPlaceHolder1_rbdDOB" onchange="resetValidationState(this);" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbdDOB_0" type="radio" name="ctl00$ContentPlaceHolder1$rbdDOB" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbdDOB_0" style="word-spacing: 0em;">DOB</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbdDOB_1" type="radio" name="ctl00$ContentPlaceHolder1$rbdDOB" value="2" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbdDOB_1" style="word-spacing: 0em;">DOI</label></td>
		</tr>
	</tbody></table>
                                                   
                                                </div>
                                            </div>
                                            
                                           
                                        </div>

                                        

                                        <div class="row" style="word-spacing: 0em;">
                                            <div class="form-group" style="text-align: justify; word-spacing: 0em;">
                                                
                                                <p style="word-spacing: 0em;">
                                                    <span id="ctl00_ContentPlaceHolder1_lblPanError" style="font-weight: bold; word-spacing: 0em;"></span></p>
                                                <p style="word-spacing: 0em;">
                                                    <span id="ctl00_ContentPlaceHolder1_lblmsgpan" style="font-weight: bold; word-spacing: 0em;"></span></p>
                                                <span id="ctl00_ContentPlaceHolder1_lblmsgExport" style="color: green; font-weight: bold; word-spacing: 0em;"></span>
                                                     <br style="word-spacing: 0em;">
                                                    
                                              </div>
                                        </div>





                                        <div class="row" style="word-spacing: 0em;">
                                            <div id="ctl00_ContentPlaceHolder1_divPreviousYearITR" class="col-md-4" style="word-spacing: 0em;">
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label style="word-spacing: 0em;">4.2 Have you filed the ITR for Previous Year(PY) (2023-24) ITR ?</label>

                                                    <table id="ctl00_ContentPlaceHolder1_rblPreviousYearITR" disabled="disabled" onchange="resetValidationState(this);" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><span disabled="disabled" style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblPreviousYearITR_0" type="radio" name="ctl00$ContentPlaceHolder1$rblPreviousYearITR" value="1" disabled="disabled" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rblPreviousYearITR$0\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblPreviousYearITR_0" style="word-spacing: 0em;">Yes / हाँ</label></span></td><td style="word-spacing: 0em;"><span disabled="disabled" style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblPreviousYearITR_1" type="radio" name="ctl00$ContentPlaceHolder1$rblPreviousYearITR" value="2" checked="checked" disabled="disabled" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblPreviousYearITR_1" style="word-spacing: 0em;">No / नहीं</label></span></td>
		</tr>
	</tbody></table>
                                                    <span id="ctl00_ContentPlaceHolder1_rfvPreviousYearITR" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                </div>
                                            </div>

                                            
                                            <div id="ctl00_ContentPlaceHolder1_divWhetherGstn" class="col-md-4" style="word-spacing: 0em;">
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label style="word-spacing: 0em;">4.3 Do you have GSTIN ?</label>
                                                    <table id="ctl00_ContentPlaceHolder1_rblWhetherGstn" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblWhetherGstn_0" type="radio" name="ctl00$ContentPlaceHolder1$rblWhetherGstn" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblWhetherGstn_0" style="word-spacing: 0em;">Yes / हाँ</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblWhetherGstn_1" type="radio" name="ctl00$ContentPlaceHolder1$rblWhetherGstn" value="2" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblWhetherGstn_1" style="word-spacing: 0em;">No / नहीं</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblWhetherGstn_2" type="radio" name="ctl00$ContentPlaceHolder1$rblWhetherGstn" value="3" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblWhetherGstn_2" style="word-spacing: 0em;">Exempted / छूट प्राप्त</label></td>
		</tr>
	</tbody></table>
                                                    <span id="ctl00_ContentPlaceHolder1_rfvWhetherGstn" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnIsGstinUpdated" id="ctl00_ContentPlaceHolder1_hdnIsGstinUpdated" value="0" style="word-spacing: 0em;">
                                                </div>
                                            </div>
                                        </div>
                                        <div id="ctl00_ContentPlaceHolder1_div3" class="row" style="text-align: justify; word-spacing: 0em;">
                                            <span id="ctl00_ContentPlaceHolder1_cvGSTN" class="validation-error" style="color: red; display: none; word-spacing: 0em;">GSTIN is mandetory subject to the provision of CGST Act 2017 and as notified by the ministry of MSME <a href="https://udyamregistration.gov.in/docs/225669.pdf" target="_blank" style="word-spacing: 0em;">vide S.O. 1055(E) dated 05th March 2021</a>. You are advised to apply for GSTIN suitably to avoid any inconvenience.</span>
                                        </div>
                                        
                                    </div>
                                </div>

                            </div>

                        </div>

                        

                        <div id="ctl00_ContentPlaceHolder1_divpartb" class="row" style="word-spacing: 0em;">
                            <div class="col-md-12" style="word-spacing: 0em;">
                                <div class="card card-secondary" style="word-spacing: 0em;">
                                    <div class="card-header" style="word-spacing: 0em;">
                                        <h3 class="card-title" style="word-spacing: 0em;">Udyam Registration basic details</h3>
                                    </div>

                                    <div class="card-body" style="word-spacing: 0em;">
                                        <div class="tab-content" style="word-spacing: 0em;">
                                            <div class="tab-pane active" id="org" style="word-spacing: 0em;">
                                                <div style="word-spacing: 0em;">
                                                    <div class="row" style="word-spacing: 0em;">
                                                        <div class="col-md-6" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <span style="color: black; word-spacing: 0em;">
                                                                    <label for="ownernamee" style="word-spacing: 0em;">
                                                                        5. Name of Entrepreneur as per PAN/Aadhaar<span class="smallword" style="word-spacing: 0em;">(if enterprise does not have PAN)</span>
                                                                    </label>
                                                                </span>
                                                                <input name="ctl00$ContentPlaceHolder1$txtOwnernamePan" type="text" value="SHRI PRAVEEN KUMAR" maxlength="100" id="ctl00_ContentPlaceHolder1_txtOwnernamePan" disabled="disabled" tabindex="1" class="form-control" autocomplete="Off" placeholder="Name of Entrepreneur as per PAN/Aadhaar" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator47" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnPanCode" id="ctl00_ContentPlaceHolder1_hdnPanCode" value="1" style="word-spacing: 0em;">
                                                                <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnPanStatusCode" id="ctl00_ContentPlaceHolder1_hdnPanStatusCode" value="E" style="word-spacing: 0em;">
                                                                <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnPanAadhaarStatus" id="ctl00_ContentPlaceHolder1_hdnPanAadhaarStatus" value="Y" style="word-spacing: 0em;">
                                                                <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnRegType" id="ctl00_ContentPlaceHolder1_hdnRegType" value="P" style="word-spacing: 0em;">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row" style="word-spacing: 0em;">
                                                        <div class="col-md-6" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    6. Mobile Number / मोबाइल नंबर</label>
                                                                +91-<input name="ctl00$ContentPlaceHolder1$txtmobile" type="text" value="9805958530" maxlength="10" id="ctl00_ContentPlaceHolder1_txtmobile" class="form-control" autocomplete="Off" placeholder="Example:- 9999999999" onchange="MobileAvailability()" style="word-spacing: 0em;">
                                                                
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator14" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                <span id="ctl00_ContentPlaceHolder1_revMobile" class="validation-error" style="color: red; display: none; word-spacing: 0em;">Mobile no. should be of 10 digits must start with 9, 8, 7 or 6</span>
                                                                <span id="ctl00_ContentPlaceHolder1_lblMobile" style="color: red; word-spacing: 0em;"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label for="Account" style="word-spacing: 0em;">
                                                                    7. Email / ईमेल
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtemail" type="text" value="<EMAIL>" maxlength="50" id="ctl00_ContentPlaceHolder1_txtemail" class="form-control" placeholder="Example:- <EMAIL>" autocomplete="Off" onchange="EmailAvailability()" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator15" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                <span id="ctl00_ContentPlaceHolder1_revEmail" class="validation-error" style="color: red; display: none; word-spacing: 0em;">Invalid email</span>

                                                                <span id="ctl00_ContentPlaceHolder1_lblEmail" style="color: red; word-spacing: 0em;"></span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row" style="word-spacing: 0em;">
                                                        <span style="color: green; font-weight: 600; word-spacing: 0em;">Categorization of ownership of the MSMEs on the basis of %age Share/Member/Contribution of amount</span>
                                                        <table class="table table-bordered table-hover table-striped" style="word-spacing: 0em;">
                                                            <tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
                                                                <th valign="top" style="word-spacing: 0em;">Sl. No.</th>
                                                                <th valign="top" style="word-spacing: 0em;">Hindu Undivided Family (HUF)</th>
                                                                <th valign="top" style="word-spacing: 0em;">Partnership (By Share)</th>
                                                                <th valign="top" style="word-spacing: 0em;">Co-Operative (By Member)</th>
                                                                <th valign="top" style="word-spacing: 0em;">Private Limited Company (By Share)</th>
                                                                <th valign="top" style="word-spacing: 0em;">Public Limited Company (By Share)</th>
                                                                <th valign="top" style="word-spacing: 0em;">Self Help Group (By Contribution)</th>
                                                                <th valign="top" style="word-spacing: 0em;">Society (By Member)</th>
                                                                <th valign="top" style="word-spacing: 0em;">Trust (By Contribution)</th>
                                                            </tr>
                                                            <tr style="word-spacing: 0em;">
                                                                <th valign="top" style="word-spacing: 0em;">OBC</th>
                                                                <th valign="top" style="word-spacing: 0em;">As per the category</th>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                            </tr>
                                                            <tr style="word-spacing: 0em;">
                                                                <th valign="top" style="word-spacing: 0em;">SC</th>
                                                                <th valign="top" style="word-spacing: 0em;">As per the category</th>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                            </tr>
                                                            <tr style="word-spacing: 0em;">
                                                                <th valign="top" style="word-spacing: 0em;">ST</th>
                                                                <th valign="top" style="word-spacing: 0em;">As per the category</th>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                            </tr>
                                                            <tr style="word-spacing: 0em;">
                                                                <th valign="top" style="word-spacing: 0em;">Women</th>
                                                                <th valign="top" style="word-spacing: 0em;">if she is Karta</th>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                                <td valign="top" style="word-spacing: 0em;">51%</td>
                                                            </tr>
                                                            <tr style="word-spacing: 0em;">
                                                                <td valign="top" colspan="9" style="word-spacing: 0em;"><strong style="word-spacing: 0em;">In Case of proprietorship enterprise, the category of the unit would be the social category of the owner.</strong></td>
                                                            </tr>
                                                        </tbody></table>
                                                    </div>

                                                    <div id="ctl00_ContentPlaceHolder1_divProprietorship" style="word-spacing: 0em;">
                                                        <div class="row" style="word-spacing: 0em;">
                                                            <div class="col-md-12" style="word-spacing: 0em;">
                                                                <div class="form-group" style="word-spacing: 0em;">
                                                                    <label for="ifscCode" style="word-spacing: 0em;">
                                                                        8. Social Category / सामाजिक वर्ग
                                                                    </label>
                                                                    <table id="ctl00_ContentPlaceHolder1_rdbcategory" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbcategory_0" type="radio" name="ctl00$ContentPlaceHolder1$rdbcategory" value="1" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbcategory_0" style="word-spacing: 0em;">General / सामान्य</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbcategory_1" type="radio" name="ctl00$ContentPlaceHolder1$rdbcategory" value="2" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbcategory_1" style="word-spacing: 0em;">SC / अनुसूचित जाति</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbcategory_2" type="radio" name="ctl00$ContentPlaceHolder1$rdbcategory" value="3" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbcategory_2" style="word-spacing: 0em;">ST / अनुसूचित जनजाति</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbcategory_3" type="radio" name="ctl00$ContentPlaceHolder1$rdbcategory" value="4" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbcategory_3" style="word-spacing: 0em;">OBC / अन्य पिछड़ा वर्ग</label></td>
		</tr>
	</tbody></table>
                                                                    <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator16" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row" style="word-spacing: 0em;">
                                                            <div class="col-md-12" style="word-spacing: 0em;">
                                                                <div class="form-group" style="word-spacing: 0em;">
                                                                    <label for="ifscCode" style="word-spacing: 0em;">
                                                                        9. Gender / लिंग
                                                                    </label>
                                                                    <table id="ctl00_ContentPlaceHolder1_rbtGender" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbtGender_0" type="radio" name="ctl00$ContentPlaceHolder1$rbtGender" value="1" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbtGender_0" style="word-spacing: 0em;">Male / पुरूष</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbtGender_1" type="radio" name="ctl00$ContentPlaceHolder1$rbtGender" value="2" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbtGender_1" style="word-spacing: 0em;">Female / स्त्री</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbtGender_2" type="radio" name="ctl00$ContentPlaceHolder1$rbtGender" value="3" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbtGender_2" style="word-spacing: 0em;">Others / अन्य</label></td>
		</tr>
	</tbody></table>
                                                                    <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator8" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="row" style="word-spacing: 0em;">
                                                            <div class="col-md-12" style="word-spacing: 0em;">
                                                                <div class="form-group" style="word-spacing: 0em;">
                                                                    <label for="ifscCode" style="word-spacing: 0em;">
                                                                        10. Specially Abled(DIVYANG) / दिव्यांग
                                                                    </label>
                                                                    <table id="ctl00_ContentPlaceHolder1_rbtPh" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbtPh_0" type="radio" name="ctl00$ContentPlaceHolder1$rbtPh" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbtPh_0" style="word-spacing: 0em;">Yes / हाँ</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rbtPh_1" type="radio" name="ctl00$ContentPlaceHolder1$rbtPh" value="0" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rbtPh_1" style="word-spacing: 0em;">No / नहीं</label></td>
		</tr>
	</tbody></table>
                                                                    <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator17" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group" style="word-spacing: 0em;">
                                                        
                                                        

                                                    </div>

                                                    <div class="form-group" style="word-spacing: 0em;">
                                                        <label for="Ename" style="word-spacing: 0em;">
                                                            11. Name of Enterprise / उद्यम का नाम
                                                        </label>
                                                        <input name="ctl00$ContentPlaceHolder1$txtenterprisename" type="text" value="PARVEEN KUMAR" maxlength="200" id="ctl00_ContentPlaceHolder1_txtenterprisename" class="form-control" placeholder="Name of Enterprise" autocomplete="Off" style="word-spacing: 0em;">
                                                        <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator1" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                        
                                                    </div>
                                                    
                                                    <div class="form-group" style="word-spacing: 0em;">
                                                        
                                                    </div>
                                                    
                                                    <div class="form-group" style="word-spacing: 0em;">

                                                        

                                                    </div>


                                                </div>

                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label style="word-spacing: 0em;">
                                                        13. Official Address of Enterprise / कार्यालय का पता
                                                    </label>
                                                </div>


                                                <div class="col-md-12" style="word-spacing: 0em;">
                                                    <div class="row" style="word-spacing: 0em;">
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Flat/ Door/ Block No. 
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtOffFlatNo" type="text" value="VILLAGE SHILA KIPPAR" maxlength="20" id="ctl00_ContentPlaceHolder1_txtOffFlatNo" class="form-control" placeholder="Flat/ Door/ Block No." autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator30" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Name of Premises/ Building 
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtOffBuilding" type="text" value="PO DUDAR" maxlength="50" id="ctl00_ContentPlaceHolder1_txtOffBuilding" class="form-control" placeholder="Name of Premises/ Building" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator31" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Village/Town
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtOffVillageTown" type="text" value="TEHSIL SADAR MANDI" maxlength="30" id="ctl00_ContentPlaceHolder1_txtOffVillageTown" class="form-control" placeholder="Village/Town" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator32" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Block
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtOffBlock" type="text" value="MANDI" maxlength="30" id="ctl00_ContentPlaceHolder1_txtOffBlock" class="form-control" placeholder="Block" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator33" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Road/ Street/ Lane
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtOffRoadStreetLane" type="text" value="MANDI" maxlength="30" id="ctl00_ContentPlaceHolder1_txtOffRoadStreetLane" class="form-control" placeholder="Road/ Street/ Lane" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator36" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    City
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtOffCity" type="text" value="MANDI" maxlength="25" id="ctl00_ContentPlaceHolder1_txtOffCity" class="form-control" placeholder="City" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator34" style="color: red; display: none; word-spacing: 0em;">Required</span>

                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Pin
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtOffPin" type="text" value="175001" maxlength="6" id="ctl00_ContentPlaceHolder1_txtOffPin" class="form-control" placeholder="Pin" autocomplete="Off" style="word-spacing: 0em;">
                                                                
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator4" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                <span id="ctl00_ContentPlaceHolder1_RegularExpressionValidator1" class="validation-error" style="color: red; display: none; word-spacing: 0em;">Pin Code must be of 6 digit and not start with 0 and 9</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label for="State" style="word-spacing: 0em;">
                                                                    State 
                                                                </label>
                                                                <select name="ctl00$ContentPlaceHolder1$ddlstate" id="ctl00_ContentPlaceHolder1_ddlstate" disabled="disabled" title="State_Namee" class="form-control" onchange="resetValidationState(this);" style="word-spacing: 0em;">
		<option value="0" style="word-spacing: 0em;">Choose State/UT</option>
		<option value="35" style="word-spacing: 0em;">1. ANDAMAN AND NICOBAR ISLANDS / अंदमान और निकोबार द्वीपसमूह</option>
		<option value="28" style="word-spacing: 0em;">2. ANDHRA PRADESH / आन्ध्र प्रदेश </option>
		<option value="12" style="word-spacing: 0em;">3. ARUNACHAL PRADESH / अरुणाचल प्रदेश </option>
		<option value="18" style="word-spacing: 0em;">4. ASSAM / असम</option>
		<option value="10" style="word-spacing: 0em;">5. BIHAR / बिहार </option>
		<option value="4" style="word-spacing: 0em;">6. CHANDIGARH / चंडीगढ़ </option>
		<option value="22" style="word-spacing: 0em;">7. CHHATTISGARH / छत्तीसगढ़ </option>
		<option value="7" style="word-spacing: 0em;">8. DELHI / दिल्ली </option>
		<option value="30" style="word-spacing: 0em;">9. GOA / गोवा </option>
		<option value="24" style="word-spacing: 0em;">10. GUJARAT / गुजरात</option>
		<option value="6" style="word-spacing: 0em;">11. HARYANA / हरियाणा</option>
		<option selected="selected" value="2" style="word-spacing: 0em;">12. HIMACHAL PRADESH / हिमाचल प्रदेश</option>
		<option value="1" style="word-spacing: 0em;">13. JAMMU AND KASHMIR / जम्मू और कश्मीर</option>
		<option value="20" style="word-spacing: 0em;">14. JHARKHAND / झारखण्ड</option>
		<option value="29" style="word-spacing: 0em;">15. KARNATAKA / कर्णाटक</option>
		<option value="32" style="word-spacing: 0em;">16. KERALA / केरल</option>
		<option value="37" style="word-spacing: 0em;">17. LADAKH / लद्दाख</option>
		<option value="31" style="word-spacing: 0em;">18. LAKSHADWEEP / लक्षद्वीप</option>
		<option value="23" style="word-spacing: 0em;">19. MADHYA PRADESH / मध्य प्रदेश</option>
		<option value="27" style="word-spacing: 0em;">20. MAHARASHTRA / महाराष्ट्र</option>
		<option value="14" style="word-spacing: 0em;">21. MANIPUR / मणिपुर</option>
		<option value="17" style="word-spacing: 0em;">22. MEGHALAYA / मेघालय</option>
		<option value="15" style="word-spacing: 0em;">23. MIZORAM / मिज़ोरम</option>
		<option value="13" style="word-spacing: 0em;">24. NAGALAND / नागालैण्ड</option>
		<option value="21" style="word-spacing: 0em;">25. ODISHA / ओड़िशा</option>
		<option value="34" style="word-spacing: 0em;">26. PUDUCHERRY / पुडुचेरी</option>
		<option value="3" style="word-spacing: 0em;">27. PUNJAB / पंजाब</option>
		<option value="8" style="word-spacing: 0em;">28. RAJASTHAN / राजस्थान</option>
		<option value="11" style="word-spacing: 0em;">29. SIKKIM / सिक्किम</option>
		<option value="33" style="word-spacing: 0em;">30. TAMIL NADU / तमिलनाडु</option>
		<option value="36" style="word-spacing: 0em;">31. TELANGANA / तेलंगाना</option>
		<option value="38" style="word-spacing: 0em;">32. THE DADRA AND NAGAR HAVELI AND DAMAN AND DIU / दादरा और नगर हवेली और दमन और दीव</option>
		<option value="16" style="word-spacing: 0em;">33. TRIPURA / त्रिपुरा</option>
		<option value="9" style="word-spacing: 0em;">34. UTTAR PRADESH / उत्तर प्रदेश</option>
		<option value="5" style="word-spacing: 0em;">35. UTTARAKHAND / उत्तराखण्ड</option>
		<option value="19" style="word-spacing: 0em;">36. WEST BENGAL / पश्चिम बंगाल</option>

	</select>
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator5" style="color: red; display: none; word-spacing: 0em;">Required</span>

                                                            </div>

                                                        </div>

                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label for="District" style="word-spacing: 0em;">
                                                                    District</label>
                                                                
                                                                <select name="ctl00$ContentPlaceHolder1$ddlDistrict" id="ctl00_ContentPlaceHolder1_ddlDistrict" disabled="disabled" class="form-control" onchange="resetValidationState(this);" style="word-spacing: 0em;">
		<option value="0" style="word-spacing: 0em;">Choose District</option>
		<option value="52" style="word-spacing: 0em;">1. BILASPUR / बिलासपुर</option>
		<option value="45" style="word-spacing: 0em;">2. CHAMBA / चंबा</option>
		<option value="50" style="word-spacing: 0em;">3. HAMIRPUR / हमीरपुर</option>
		<option value="46" style="word-spacing: 0em;">4. KANGRA / कांगड़ा</option>
		<option value="56" style="word-spacing: 0em;">5. KINNAUR / किन्नौर</option>
		<option value="48" style="word-spacing: 0em;">6. KULLU / कुल्लू</option>
		<option value="47" style="word-spacing: 0em;">7. LAHUL AND SPITI / लाहौल और स्पीति</option>
		<option selected="selected" value="49" style="word-spacing: 0em;">8. MANDI / मंडी</option>
		<option value="55" style="word-spacing: 0em;">9. SHIMLA / शिमला</option>
		<option value="54" style="word-spacing: 0em;">10. SIRMAUR / सिरमौर</option>
		<option value="53" style="word-spacing: 0em;">11. SOLAN / सोलन</option>
		<option value="51" style="word-spacing: 0em;">12. UNA / ऊना</option>

	</select>
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator6" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                
                                                            </div>
                                                        </div>

                                                      
                                                    <div class="col-md-4" style="word-spacing: 0em;">
                                                <div class="form-group" style="word-spacing: 0em;">
                                                 <label for="District" style="word-spacing: 0em;">
                                                    Latitude</label>
                                                 <input type="hidden" name="ctl00$ContentPlaceHolder1$hdndtcode" id="ctl00_ContentPlaceHolder1_hdndtcode" value="22" style="word-spacing: 0em;">
                                                   <input name="ctl00$ContentPlaceHolder1$txtLat" type="text" value="31.709834318647896" maxlength="102" id="ctl00_ContentPlaceHolder1_txtLat" disabled="disabled" class="form-control" autocomplete="Off" style="word-spacing: 0em;">
                                                    <span id="ctl00_ContentPlaceHolder1_reqlatude" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                            </div>
                             </div>
                                                    <div class="col-md-4" style="word-spacing: 0em;">
                                    <div class="form-group" style="word-spacing: 0em;">
                                                 <label for="District" style="word-spacing: 0em;">
                                                    Longitude</label>                                                 
                                                 <input name="ctl00$ContentPlaceHolder1$txtLngt" type="text" value="76.98140766833365" maxlength="102" id="ctl00_ContentPlaceHolder1_txtLngt" disabled="disabled" class="form-control" autocomplete="Off" style="word-spacing: 0em;">
                                                <span id="ctl00_ContentPlaceHolder1_reqlngt" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                 </div>
                            </div>
                                                    <div class="col-md-4" style="margin-top: 22px; word-spacing: 0em;">

                                 
                              <input type="submit" name="ctl00$ContentPlaceHolder1$Button1" value="Get Latitude &amp; Longitude" onclick="Call_Map();" id="ctl00_ContentPlaceHolder1_Button1" class="btn btn-primary" style="margin-top: 10px; word-spacing: 0em;">  </div>   


                                                    </div>
                                                </div>
                                                
                                                
                                                
                                                
                                                
                                                
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label for="Ename" style="word-spacing: 0em;">
                                                        14. Previous EM-II/UAM Registration Number, If Any / पिछले EM-II/UAM पंजीकरण संख्या, यदि कोई है
                                                    </label>
                                                    <table id="ctl00_ContentPlaceHolder1_rdbPreviousEM" onchange="resetValidationState(this);" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbPreviousEM_0" type="radio" name="ctl00$ContentPlaceHolder1$rdbPreviousEM" value="0" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbPreviousEM_0" style="word-spacing: 0em;">N/A</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbPreviousEM_1" type="radio" name="ctl00$ContentPlaceHolder1$rdbPreviousEM" value="2" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rdbPreviousEM$1\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbPreviousEM_1" style="word-spacing: 0em;">EM-II</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbPreviousEM_2" type="radio" name="ctl00$ContentPlaceHolder1$rdbPreviousEM" value="4" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rdbPreviousEM$2\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbPreviousEM_2" style="word-spacing: 0em;">Previous UAM</label></td>
		</tr>
	</tbody></table>
                                                    
                                                    <span id="ctl00_ContentPlaceHolder1_RFVPreviousNumber" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                </div>
                                                
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label style="word-spacing: 0em;">
                                                        15. Status of Enterprise
                                                    </label>
                                                </div>
                                                <div class="col-md-12" style="word-spacing: 0em;">
                                                    <div class="row" style="word-spacing: 0em;">
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label for="nic-code" style="word-spacing: 0em;">
                                                                    a. Date of Incorporation/registration
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtdateIncorporation" type="text" value="21/04/2022" id="ctl00_ContentPlaceHolder1_txtdateIncorporation" class="form-control" onchange="return calc_dateIncop(this.id);" autocomplete="Off" placeholder="DD/MM/YYYY" style="word-spacing: 0em;">
                                                                

                                                                <span id="ctl00_ContentPlaceHolder1_CustomValidator4" style="color: red; display: none; word-spacing: 0em;">Invalid date</span>
                                                                <br style="word-spacing: 0em;">
                                                                
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator37" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            <div id="ctl00_ContentPlaceHolder1_CalendarExtender1_container" class="cal_Theme1"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_popupDiv" class="ajax__calendar_container" style="visibility: hidden; position: absolute; left: 7px; top: 70px; width: 183px; display: none;"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_header" class="ajax__calendar_header"><div><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_prevArrow" class="ajax__calendar_prev"></div></div><div><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_nextArrow" class="ajax__calendar_next"></div></div><div><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_title" class="ajax__calendar_title">April, 2022</div></div></div><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_body" class="ajax__calendar_body"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_days" class="ajax__calendar_days"><table id="ctl00_ContentPlaceHolder1_CalendarExtender1_daysTable" cellpadding="0" cellspacing="0" border="0" style="margin: auto;"><thead id="ctl00_ContentPlaceHolder1_CalendarExtender1_daysTableHeader"><tr id="ctl00_ContentPlaceHolder1_CalendarExtender1_daysTableHeaderRow"><td><div class="ajax__calendar_dayname">Su</div></td><td><div class="ajax__calendar_dayname">Mo</div></td><td><div class="ajax__calendar_dayname">Tu</div></td><td><div class="ajax__calendar_dayname">We</div></td><td><div class="ajax__calendar_dayname">Th</div></td><td><div class="ajax__calendar_dayname">Fr</div></td><td><div class="ajax__calendar_dayname">Sa</div></td></tr></thead><tbody id="ctl00_ContentPlaceHolder1_CalendarExtender1_daysBody"><tr><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_0_0" class="ajax__calendar_day" title="Sunday, March 27, 2022">27</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_0_1" class="ajax__calendar_day" title="Monday, March 28, 2022">28</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_0_2" class="ajax__calendar_day" title="Tuesday, March 29, 2022">29</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_0_3" class="ajax__calendar_day" title="Wednesday, March 30, 2022">30</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_0_4" class="ajax__calendar_day" title="Thursday, March 31, 2022">31</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_0_5" class="ajax__calendar_day" title="Friday, April 01, 2022">1</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_0_6" class="ajax__calendar_day" title="Saturday, April 02, 2022">2</div></td></tr><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_1_0" class="ajax__calendar_day" title="Sunday, April 03, 2022">3</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_1_1" class="ajax__calendar_day" title="Monday, April 04, 2022">4</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_1_2" class="ajax__calendar_day" title="Tuesday, April 05, 2022">5</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_1_3" class="ajax__calendar_day" title="Wednesday, April 06, 2022">6</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_1_4" class="ajax__calendar_day" title="Thursday, April 07, 2022">7</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_1_5" class="ajax__calendar_day" title="Friday, April 08, 2022">8</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_1_6" class="ajax__calendar_day" title="Saturday, April 09, 2022">9</div></td></tr><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_2_0" class="ajax__calendar_day" title="Sunday, April 10, 2022">10</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_2_1" class="ajax__calendar_day" title="Monday, April 11, 2022">11</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_2_2" class="ajax__calendar_day" title="Tuesday, April 12, 2022">12</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_2_3" class="ajax__calendar_day" title="Wednesday, April 13, 2022">13</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_2_4" class="ajax__calendar_day" title="Thursday, April 14, 2022">14</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_2_5" class="ajax__calendar_day" title="Friday, April 15, 2022">15</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_2_6" class="ajax__calendar_day" title="Saturday, April 16, 2022">16</div></td></tr><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_3_0" class="ajax__calendar_day" title="Sunday, April 17, 2022">17</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_3_1" class="ajax__calendar_day" title="Monday, April 18, 2022">18</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_3_2" class="ajax__calendar_day" title="Tuesday, April 19, 2022">19</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_3_3" class="ajax__calendar_day" title="Wednesday, April 20, 2022">20</div></td><td class="ajax__calendar_active"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_3_4" class="ajax__calendar_day" title="Thursday, April 21, 2022">21</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_3_5" class="ajax__calendar_day" title="Friday, April 22, 2022">22</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_3_6" class="ajax__calendar_day" title="Saturday, April 23, 2022">23</div></td></tr><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_4_0" class="ajax__calendar_day" title="Sunday, April 24, 2022">24</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_4_1" class="ajax__calendar_day" title="Monday, April 25, 2022">25</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_4_2" class="ajax__calendar_day" title="Tuesday, April 26, 2022">26</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_4_3" class="ajax__calendar_day" title="Wednesday, April 27, 2022">27</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_4_4" class="ajax__calendar_day" title="Thursday, April 28, 2022">28</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_4_5" class="ajax__calendar_day" title="Friday, April 29, 2022">29</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_4_6" class="ajax__calendar_day" title="Saturday, April 30, 2022">30</div></td></tr><tr><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_5_0" class="ajax__calendar_day" title="Sunday, May 01, 2022">1</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_5_1" class="ajax__calendar_day" title="Monday, May 02, 2022">2</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_5_2" class="ajax__calendar_day" title="Tuesday, May 03, 2022">3</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_5_3" class="ajax__calendar_day" title="Wednesday, May 04, 2022">4</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_5_4" class="ajax__calendar_day" title="Thursday, May 05, 2022">5</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_5_5" class="ajax__calendar_day" title="Friday, May 06, 2022">6</div></td><td class="ajax__calendar_other"><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_day_5_6" class="ajax__calendar_day" title="Saturday, May 07, 2022">7</div></td></tr></tbody></table></div><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_months" class="ajax__calendar_months" style="display: none; visibility: hidden;"><table id="ctl00_ContentPlaceHolder1_CalendarExtender1_monthsTable" cellpadding="0" cellspacing="0" border="0" style="margin: auto;"><tbody id="ctl00_ContentPlaceHolder1_CalendarExtender1_monthsBody"><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_0_0" class="ajax__calendar_month"><br>Jan</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_0_1" class="ajax__calendar_month"><br>Feb</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_0_2" class="ajax__calendar_month"><br>Mar</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_0_3" class="ajax__calendar_month"><br>Apr</div></td></tr><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_1_0" class="ajax__calendar_month"><br>May</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_1_1" class="ajax__calendar_month"><br>Jun</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_1_2" class="ajax__calendar_month"><br>Jul</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_1_3" class="ajax__calendar_month"><br>Aug</div></td></tr><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_2_0" class="ajax__calendar_month"><br>Sep</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_2_1" class="ajax__calendar_month"><br>Oct</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_2_2" class="ajax__calendar_month"><br>Nov</div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_month_2_3" class="ajax__calendar_month"><br>Dec</div></td></tr></tbody></table></div><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_years" class="ajax__calendar_years" style="display: none; visibility: hidden;"><table id="ctl00_ContentPlaceHolder1_CalendarExtender1_yearsTable" cellpadding="0" cellspacing="0" border="0" style="margin: auto;"><tbody id="ctl00_ContentPlaceHolder1_CalendarExtender1_yearsBody"><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_0_0" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_0_1" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_0_2" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_0_3" class="ajax__calendar_year"></div></td></tr><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_1_0" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_1_1" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_1_2" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_1_3" class="ajax__calendar_year"></div></td></tr><tr><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_2_0" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_2_1" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_2_2" class="ajax__calendar_year"></div></td><td><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_year_2_3" class="ajax__calendar_year"></div></td></tr></tbody></table></div></div><div><div id="ctl00_ContentPlaceHolder1_CalendarExtender1_today" class="ajax__calendar_footer ajax__calendar_today">Today: August 2, 2025</div></div></div></div></div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label for="nic-code" style="word-spacing: 0em;">
                                                                    b. Whether production/business commenced</label>

                                                                <table id="ctl00_ContentPlaceHolder1_rblcommenced" onchange="resetValidationState(this);" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblcommenced_0" type="radio" name="ctl00$ContentPlaceHolder1$rblcommenced" value="1" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rblcommenced$0\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblcommenced_0" style="word-spacing: 0em;"> Yes &nbsp;&nbsp;</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblcommenced_1" type="radio" name="ctl00$ContentPlaceHolder1$rblcommenced" value="0" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblcommenced_1" style="word-spacing: 0em;"> No </label></td>
		</tr>
	</tbody></table>
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator38" style="color: red; display: none; word-spacing: 0em;">Required</span>

                                                            </div>
                                                        </div>
                                                        
                                                    </div>
                                                </div>

                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label style="word-spacing: 0em;">
                                                        16. Bank Details / बैंक विवरण
                                                    </label>
                                                </div>

                                                <div class="col-md-12" style="word-spacing: 0em;">
                                                    <div class="row" style="word-spacing: 0em;">

                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label for="EmailId" style="word-spacing: 0em;">
                                                                    Bank Name / बैंक विवरण</label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtBankName" type="text" value="PUNJAB NATIONAL BANK" maxlength="30" id="ctl00_ContentPlaceHolder1_txtBankName" class="form-control" autocomplete="Off" placeholder="Enter Bank Name" style="text-transform: uppercase; word-spacing: 0em;">
                                                                
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator35" style="color: red; display: none; word-spacing: 0em;">Required</span>

                                                                
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label for="EmailId" style="word-spacing: 0em;">
                                                                    IFS Code / आईएफएस कोड</label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtifsccode" type="text" value="PUNB0031100" maxlength="11" id="ctl00_ContentPlaceHolder1_txtifsccode" class="form-control" autocomplete="Off" placeholder="Example:- SBIN0001624" style="text-transform: uppercase; word-spacing: 0em;">
                                                                
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator13" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                <span id="ctl00_ContentPlaceHolder1_RegularExpressionValidator4" class="validation-error" style="color: red; display: none; word-spacing: 0em;">IFS code must be of first 4 of alphabets after that 0  and last 6 of alpha-numeric </span>
                                                                
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label for="mobile" style="word-spacing: 0em;">
                                                                    Bank Account Number / बैंक खाता संख्या
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtaccountno" type="text" value="****************" maxlength="18" id="ctl00_ContentPlaceHolder1_txtaccountno" class="form-control" placeholder="Example:- **********" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator11" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                
                                                                <span id="ctl00_ContentPlaceHolder1_RegularExpressionValidator3" style="color: red; display: none; word-spacing: 0em;">Please enter numbers only</span>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label for="distric" style="word-spacing: 0em;">
                                                        19. Number of persons employed / नियोजित व्यक्तियों की संख्या
                                                    </label>
                                                </div>

                                                <div class="row" style="word-spacing: 0em;">

                                                    <div class="col-md-3" style="word-spacing: 0em;">
                                                        <div class="form-group" style="word-spacing: 0em;">
                                                            <label for="distric" style="word-spacing: 0em;">
                                                                Male / पुरूष
                                                            </label>
                                                            <input name="ctl00$ContentPlaceHolder1$txtNoofpersonMale" type="text" value="1" maxlength="4" id="ctl00_ContentPlaceHolder1_txtNoofpersonMale" class="form-control" onkeyup="CaculateEmp()" autocomplete="Off" placeholder="Example:- 20" style="word-spacing: 0em;">
                                                            <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator39" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            
                                                            <span id="ctl00_ContentPlaceHolder1_RangeValidator1" style="color: red; visibility: hidden; word-spacing: 0em;">The value must be integer and between 0 to 6000</span>

                                                        </div>
                                                    </div>
                                                    <div class="col-md-3" style="word-spacing: 0em;">
                                                        <div class="form-group" style="word-spacing: 0em;">
                                                            <label for="distric" style="word-spacing: 0em;">
                                                                Female / स्त्री
                                                            </label>
                                                            <input name="ctl00$ContentPlaceHolder1$txtNoofpersonFemale" type="text" value="0" maxlength="4" id="ctl00_ContentPlaceHolder1_txtNoofpersonFemale" class="form-control" onkeyup="CaculateEmp()" autocomplete="Off" placeholder="Example:- 20" style="word-spacing: 0em;">
                                                            <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator40" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            
                                                            <span id="ctl00_ContentPlaceHolder1_RangeValidator2" style="color: red; visibility: hidden; word-spacing: 0em;">The value must be integer and between 0 to 6000</span>

                                                        </div>
                                                    </div>
                                                    <div class="col-md-3" style="word-spacing: 0em;">
                                                        <div class="form-group" style="word-spacing: 0em;">
                                                            <label for="distric" style="word-spacing: 0em;">
                                                                Others / अन्य
                                                            </label>
                                                            <input name="ctl00$ContentPlaceHolder1$txtNoofpersonOthers" type="text" value="0" maxlength="4" id="ctl00_ContentPlaceHolder1_txtNoofpersonOthers" class="form-control" onkeyup="CaculateEmp()" autocomplete="Off" placeholder="Example:- 20" style="word-spacing: 0em;">
                                                            <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator41" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            
                                                            <span id="ctl00_ContentPlaceHolder1_RangeValidator3" style="color: red; visibility: hidden; word-spacing: 0em;">The value must be integer and between 0 to 6000</span>

                                                        </div>
                                                    </div>
                                                    <div class="col-md-3" style="word-spacing: 0em;">
                                                        <div class="form-group" style="word-spacing: 0em;">
                                                            <label for="distric" style="word-spacing: 0em;">
                                                                Total / संपूर्ण
                                                            </label>
                                                            <input name="ctl00$ContentPlaceHolder1$txttotalemp" type="text" value="1" maxlength="5" id="ctl00_ContentPlaceHolder1_txttotalemp" disabled="disabled" class="form-control" autocomplete="Off" placeholder="Example:- 20" style="word-spacing: 0em;">
                                                            <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator9" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            
                                                            <span id="ctl00_ContentPlaceHolder1_RegularExpressionValidator2" style="color: red; display: none; word-spacing: 0em;">Please enter numbers only</span>
                                                            <span id="ctl00_ContentPlaceHolder1_Range1" style="color: red; visibility: hidden; word-spacing: 0em;">The value must be integer and between 1 to 18000</span>

                                                        </div>
                                                    </div>

                                                </div>
                                                 <div class="row" style="word-spacing: 0em;">
                                          <div class="form-group" style="word-spacing: 0em;">
                                                <div class="checkbox" style="word-spacing: 0em;">
                                                   
                                                    <span disabled="disabled" style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_chkboxEmp" type="checkbox" name="ctl00$ContentPlaceHolder1$chkboxEmp" checked="checked" disabled="disabled" style="word-spacing: 0em;"></span>
                                                     &nbsp;I hereby declare that I am fully aware of the provisions of Child &amp; Adolescent Labour (Prohibition and Regulation) Act, 1986 and no child is employed in my enterprise. / मैं एतद्द्वारा घोषणा करता हूं कि मैं बाल एवं किशोर श्रम (निषेध और विनियमन) अधिनियम, 1986 के प्रावधानों से पूरी तरह अवगत हूं और मेरे उद्यम में कोई भी बच्चा नियोजित नहीं है।
                                                    <br style="word-spacing: 0em;">
                                                    <span id="ctl00_ContentPlaceHolder1_CustomValidator1" style="color: red; display: none; word-spacing: 0em;">You must Agree Declerations.</span>
                                                </div>
                                            </div>
                                           </div>
                                                <div class="row" style="word-spacing: 0em;">
                                                    <div class="col-md-12" style="word-spacing: 0em;">
                                                        <div class="form-group" style="word-spacing: 0em;">
                                                            <div style="text-align: left; word-spacing: 0em;">
                                                                <b style="word-spacing: 0em;">Investment in Plant and Machinery OR Equipment (in Rs.)</b>
                                                            </div>
                                                            <table class="table table-bordered" width="100%" style="word-spacing: 0em;">
                                                                
                                                                        <tbody style="word-spacing: 0em;"><tr style="font-size: 14px; padding: 5px; word-spacing: 0em;">
                                                                            <th align="center" valign="top" style="word-spacing: 0em;">SNo.
                                                                            </th>
                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Financial Year
                                                                            </th>
                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Enterprise Type
                                                                            </th>
                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Written Down Value (WDV)
                                                                            </th>
                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Exclusion of cost of Pollution Control,
                                                        <br style="word-spacing: 0em;">
                                                                                Research &amp; Development and Industrial Safety
                                                                                 Devices
                                                                            </th>
                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Net Investment in Plant and Machinery OR Equipment[(A)-(B)]
                                                                            </th>
                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Total Turnover (A)
                                                                            </th>

                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Export Turnover (B)
                                                                            </th>

                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Net Turnover [(A)-(B)]
                                                                            </th>

                                                                            <th align="center" valign="top" style="word-spacing: 0em;">Is ITR Filled?
                                                                            </th>

                                                                            <th align="center" valign="top" style="word-spacing: 0em;">ITR Type
                                                                            </th>
                                                                        </tr>
                                                                    
                                                                        <tr style="font-size: 14px; padding: 5px; word-spacing: 0em;">
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                1
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                2023-24
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                Micro
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                No
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                NA
                                                                            </td>

                                                                        </tr>
                                                                    
                                                                        <tr style="font-size: 14px; padding: 5px; word-spacing: 0em;">
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                2
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                2022-23
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                Micro
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                No
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                NA
                                                                            </td>

                                                                        </tr>
                                                                    
                                                                        <tr style="font-size: 14px; padding: 5px; word-spacing: 0em;">
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                3
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                2021-22
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                Micro
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                0.00
                                                                            </td>

                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                No
                                                                            </td>
                                                                            <td valign="top" style="word-spacing: 0em;">
                                                                                NA
                                                                            </td>

                                                                        </tr>
                                                                    
                                                            </tbody></table>

                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row" style="word-spacing: 0em;">
                                                    <div class="col-7 form-group" style="word-spacing: 0em;">
                                                        <label for="GeM" style="word-spacing: 0em;">
                                                            22. Are you interested to get registered on Government e-Market (GeM) Portal
                                                        </label>
                                                    </div>
                                                    <div class="col-5 form-group" style="word-spacing: 0em;">

                                                        <table id="ctl00_ContentPlaceHolder1_rblGeM" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblGeM_0" type="radio" name="ctl00$ContentPlaceHolder1$rblGeM" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblGeM_0" style="word-spacing: 0em;">Yes / हाँ</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblGeM_1" type="radio" name="ctl00$ContentPlaceHolder1$rblGeM" value="0" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblGeM_1" style="word-spacing: 0em;">No / नहीं</label></td>
		</tr>
	</tbody></table>
                                                        <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator44" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    </div>
                                                    <div class=" col-7 form-group" style="word-spacing: 0em;">
                                                        <label for="GeM" style="word-spacing: 0em;">
                                                            23. Are you interested to get registered on TReDS Portals(one or more)
                                                        </label>
                                                    </div>
                                                    <div class=" col-5 form-group" style="word-spacing: 0em;">

                                                        <table id="ctl00_ContentPlaceHolder1_rblTReDS" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblTReDS_0" type="radio" name="ctl00$ContentPlaceHolder1$rblTReDS" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblTReDS_0" style="word-spacing: 0em;">Yes / हाँ</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblTReDS_1" type="radio" name="ctl00$ContentPlaceHolder1$rblTReDS" value="0" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblTReDS_1" style="word-spacing: 0em;">No / नहीं </label></td>
		</tr>
	</tbody></table>
                                                        <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator46" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    </div>

                                                     <div class="col-7 form-group" style="word-spacing: 0em;">
                                                        <label for="GeM" style="word-spacing: 0em;">
                                                        24. Are you interested to get registered on National Career Service(NCS) Portal
                                                        </label>
                                                    </div>
                                                    <div class="col-5 form-group" style="word-spacing: 0em;">                                                       
                                                        <table id="ctl00_ContentPlaceHolder1_rblNCS" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblNCS_0" type="radio" name="ctl00$ContentPlaceHolder1$rblNCS" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblNCS_0" style="word-spacing: 0em;">Yes / हाँ</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblNCS_1" type="radio" name="ctl00$ContentPlaceHolder1$rblNCS" value="2" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblNCS_1" style="word-spacing: 0em;">No / नहीं</label></td>
		</tr>
	</tbody></table>
                                                        <span id="ctl00_ContentPlaceHolder1_rfvNCS" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    </div>

                                                    <div class="col-7 form-group" style="word-spacing: 0em;">
                                                        <label for="NSIC" style="word-spacing: 0em;">
                                                        25. Are you interested to get registered on NSIC B2B Portal
                                                        </label>
                                                    </div>
                                                    <div class="col-5 form-group" style="word-spacing: 0em;">                                                       
                                                        <table id="ctl00_ContentPlaceHolder1_rblnsic" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblnsic_0" type="radio" name="ctl00$ContentPlaceHolder1$rblnsic" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblnsic_0" style="word-spacing: 0em;">Yes / हाँ</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblnsic_1" type="radio" name="ctl00$ContentPlaceHolder1$rblnsic" value="2" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblnsic_1" style="word-spacing: 0em;">No / नहीं</label></td>
		</tr>
	</tbody></table>
                                                        <span id="ctl00_ContentPlaceHolder1_rfvNSIC" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    </div>


                                                     <div class="col-md-7 form-group" style="display: none; word-spacing: 0em;">
                                                <label for="GeM" style="word-spacing: 0em;">
                                                    <div class="tooltip-wrap" style="word-spacing: 0em;">
                                                     26. Are you interested in availing Free .IN Domain and a business email ID
                                                    
                                                   
                                                   </div>
                                                </label>
                                              </div>
                                              <div class="col-md-5 form-group" style="display: none; word-spacing: 0em;">                                                       
                                                <table id="ctl00_ContentPlaceHolder1_rblnixi" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblnixi_0" type="radio" name="ctl00$ContentPlaceHolder1$rblnixi" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblnixi_0" style="word-spacing: 0em;">Yes / हाँ</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblnixi_1" type="radio" name="ctl00$ContentPlaceHolder1$rblnixi" value="2" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblnixi_1" style="word-spacing: 0em;">No / नहीं</label></td>
		</tr>
	</tbody></table>
                                                
                                               </div>


                                                    <div class="col-md-7 form-group" style="word-spacing: 0em;">
                                                <label for="GeM" style="word-spacing: 0em;">
                                                    <div class="tooltip-wrap" style="word-spacing: 0em;">
                                                     26. Are you interested in getting registered on Skill India Digital Portal 
                                                    
                                                   <i class="fa fa-info-circle" style="color: blue; word-spacing: 0em;" aria-hidden="true"></i>                                                    
                                                         <div class="tooltip-content" style="word-spacing: 0em;">For more information go to <br style="word-spacing: 0em;">
                                                         <a style="text-decoration: none; color: rgb(255, 255, 255); word-spacing: 0em;" href="https://www.skillindiadigital.gov.in/" target="_blank">
                                                             SID Portal Click here</a>    
                                                         </div>
                                                   </div>
                                                </label>
                                              </div>
                                              <div class="col-md-5 form-group" style="word-spacing: 0em;">                                                       
                                                <table id="ctl00_ContentPlaceHolder1_rblsid" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblsid_0" type="radio" name="ctl00$ContentPlaceHolder1$rblsid" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblsid_0" style="word-spacing: 0em;">Yes / हाँ</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rblsid_1" type="radio" name="ctl00$ContentPlaceHolder1$rblsid" value="2" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rblsid_1" style="word-spacing: 0em;">No / नहीं</label></td>
		</tr>
	</tbody></table>
                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator12" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                               </div>



                                                </div>
                                                <div class="form-group" style="word-spacing: 0em;">
                                                    <label for="District" style="word-spacing: 0em;">
                                                        27. District Industries Centre / जिला उद्योग कार्यालय
                                                    </label>
                                                    
                                                    <select name="ctl00$ContentPlaceHolder1$ddlDIC" id="ctl00_ContentPlaceHolder1_ddlDIC" disabled="disabled" class="form-control" style="word-spacing: 0em;">
		<option value="0" style="word-spacing: 0em;">Choose DIC</option>
		<option selected="selected" value="97" style="word-spacing: 0em;">1. MANDI / मंडी</option>

	</select>
                                                    <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator312" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    
                                                </div>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="ctl00_ContentPlaceHolder1_div11" class="row" style="word-spacing: 0em;">
                                <div class="col-md-12" style="word-spacing: 0em;">
                                    <div class="card card-secondary" style="word-spacing: 0em;">
                                        <div class="card-header" style="word-spacing: 0em;">
                                            <h3 class="card-title" style="word-spacing: 0em;">Update Unit and Plant Location Details</h3>
                                        </div>
                                        <div class="card-body" style="word-spacing: 0em;">
                                            <div class="tab-content" style="word-spacing: 0em;">
                                                <div class="tab-pane active" id="org" style="word-spacing: 0em;">

                                                    <div class="row" style="word-spacing: 0em;">
                                                        <div class="col-md-8" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <span style="color: black; word-spacing: 0em;">
                                                                    <label for="ownernamee" style="word-spacing: 0em;">
                                                                        Plant/Unit Name / इकाई का नाम
                                                                    </label>
                                                                </span>
                                                                <input name="ctl00$ContentPlaceHolder1$txtUnitName" type="text" maxlength="100" id="ctl00_ContentPlaceHolder1_txtUnitName" tabindex="1" class="form-control" autocomplete="Off" placeholder="Unit Name" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_rfvUnitName" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <div style="margin-top: 31px; word-spacing: 0em;">
                                                                    <input type="submit" name="ctl00$ContentPlaceHolder1$btnAddUnit" value="Add Unit" onclick="javascript:WebForm_DoPostBackWithOptions(new WebForm_PostBackOptions(&quot;ctl00$ContentPlaceHolder1$btnAddUnit&quot;, &quot;&quot;, true, &quot;UN&quot;, &quot;&quot;, false, false))" id="ctl00_ContentPlaceHolder1_btnAddUnit" class="btn btn-primary" style="word-spacing: 0em;">
                                                                </div>
                                                                <label style="word-spacing: 0em;">
                                                                    <span id="ctl00_ContentPlaceHolder1_lblunitadd" style="color: red; font-weight: bold; word-spacing: 0em;"></span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row" style="word-spacing: 0em;">
                                                        <div class="col-md-12" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <div style="word-spacing: 0em;">
                                                                    <span id="ctl00_ContentPlaceHolder1_lblUnitError" style="color: red; font-weight: bold; word-spacing: 0em;"></span>
                                                                    <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnUnitDetail" id="ctl00_ContentPlaceHolder1_hdnUnitDetail" style="word-spacing: 0em;">
                                                                </div>
                                                                <div class="box-body table-responsive no-padding" style="word-spacing: 0em;">
                                                                    <div style="word-spacing: 0em;">
		<table cellspacing="0" rules="all" class="table table-bordered table-hover table-striped" border="1" id="ctl00_ContentPlaceHolder1_gvUnitDetailUAM" style="width: 100%; border-collapse: collapse; word-spacing: 0em;">
			<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
				<th scope="col" style="word-spacing: 0em;">SNo.</th><th scope="col" style="word-spacing: 0em;">Unit Name</th><th scope="col" style="word-spacing: 0em;">UAM</th><th scope="col" style="word-spacing: 0em;">Delete</th>
			</tr><tr style="word-spacing: 0em;">
				<td style="width: 5%; word-spacing: 0em;">
                                                                                    <span style="word-spacing: 0em;">
                                                                                        1</span>
                                                                                </td><td style="word-spacing: 0em;">PARVEEN KUMAR</td><td style="word-spacing: 0em;">&nbsp;</td><td style="width: 10%; word-spacing: 0em;">
                                                                                    
                                                                                    
                                                                                    <input type="image" name="ctl00$ContentPlaceHolder1$gvUnitDetailUAM$ctl02$ibUnitDelete" id="ctl00_ContentPlaceHolder1_gvUnitDetailUAM_ctl02_ibUnitDelete" src="../Images/delete.jpg" style="border-width: 0px; word-spacing: 0em;">
                                                                                </td>
			</tr>
		</tbody></table>
	</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group" style="word-spacing: 0em;">
                                                        <label style="word-spacing: 0em;">
                                                            12. Location of Plant(s)/Unit(s)
                                                        </label>
                                                    </div>

                                                    <div class="row" style="word-spacing: 0em;">
                                                        <div class="col-md-12" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Unit Name / इकाई का नाम
                                                                </label>

                                                                <select name="ctl00$ContentPlaceHolder1$ddlUnitName" id="ctl00_ContentPlaceHolder1_ddlUnitName" class="form-control" style="word-spacing: 0em;">
		<option value="0" style="word-spacing: 0em;">Select</option>
		<option value="1" style="word-spacing: 0em;">PARVEEN KUMAR</option>

	</select>
                                                                <span id="ctl00_ContentPlaceHolder1_rfvddlUnitName" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Flat/Door/Block No.
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtPFlat" type="text" maxlength="20" id="ctl00_ContentPlaceHolder1_txtPFlat" class="form-control" placeholder="Flat/Door/Block No." autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator20" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Name of Premises/ Building 
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtPBuilding" type="text" maxlength="50" id="ctl00_ContentPlaceHolder1_txtPBuilding" class="form-control" placeholder="Name of Premises/ Building" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator21" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Village/Town
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtPVillageTown" type="text" maxlength="30" id="ctl00_ContentPlaceHolder1_txtPVillageTown" class="form-control" placeholder="Village/Town" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator49" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Block 
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtPBlock" type="text" maxlength="50" id="ctl00_ContentPlaceHolder1_txtPBlock" class="form-control" placeholder="Block" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator23" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Road/ Street/ Lane
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtPRoadStreetLane" type="text" maxlength="30" id="ctl00_ContentPlaceHolder1_txtPRoadStreetLane" class="form-control" placeholder="Road/ Street/ Lane" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator22" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    City
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtPCity" type="text" maxlength="25" id="ctl00_ContentPlaceHolder1_txtPCity" class="form-control" placeholder="City" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator25" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    Pin
                                                                </label>
                                                                <input name="ctl00$ContentPlaceHolder1$txtPpin" type="text" maxlength="6" id="ctl00_ContentPlaceHolder1_txtPpin" class="form-control" placeholder="Pin" autocomplete="Off" style="word-spacing: 0em;">
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator26" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                
                                                                <span id="ctl00_ContentPlaceHolder1_RegularExpressionValidator6" class="validation-error" style="color: red; display: none; word-spacing: 0em;">Pin Code must be of 6 digit and not start with 0 and 9</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    State
                                                                </label>
                                                                <select name="ctl00$ContentPlaceHolder1$ddlPState" onchange="resetValidationState(this);setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$ddlPState\',\'\')', 0)" id="ctl00_ContentPlaceHolder1_ddlPState" title="State_Namee" class="form-control" style="word-spacing: 0em;">
		<option selected="selected" value="0" style="word-spacing: 0em;">Choose State/UT</option>
		<option value="35" style="word-spacing: 0em;">1. ANDAMAN AND NICOBAR ISLANDS / अंदमान और निकोबार द्वीपसमूह</option>
		<option value="28" style="word-spacing: 0em;">2. ANDHRA PRADESH / आन्ध्र प्रदेश </option>
		<option value="12" style="word-spacing: 0em;">3. ARUNACHAL PRADESH / अरुणाचल प्रदेश </option>
		<option value="18" style="word-spacing: 0em;">4. ASSAM / असम</option>
		<option value="10" style="word-spacing: 0em;">5. BIHAR / बिहार </option>
		<option value="4" style="word-spacing: 0em;">6. CHANDIGARH / चंडीगढ़ </option>
		<option value="22" style="word-spacing: 0em;">7. CHHATTISGARH / छत्तीसगढ़ </option>
		<option value="7" style="word-spacing: 0em;">8. DELHI / दिल्ली </option>
		<option value="30" style="word-spacing: 0em;">9. GOA / गोवा </option>
		<option value="24" style="word-spacing: 0em;">10. GUJARAT / गुजरात</option>
		<option value="6" style="word-spacing: 0em;">11. HARYANA / हरियाणा</option>
		<option value="2" style="word-spacing: 0em;">12. HIMACHAL PRADESH / हिमाचल प्रदेश</option>
		<option value="1" style="word-spacing: 0em;">13. JAMMU AND KASHMIR / जम्मू और कश्मीर</option>
		<option value="20" style="word-spacing: 0em;">14. JHARKHAND / झारखण्ड</option>
		<option value="29" style="word-spacing: 0em;">15. KARNATAKA / कर्णाटक</option>
		<option value="32" style="word-spacing: 0em;">16. KERALA / केरल</option>
		<option value="37" style="word-spacing: 0em;">17. LADAKH / लद्दाख</option>
		<option value="31" style="word-spacing: 0em;">18. LAKSHADWEEP / लक्षद्वीप</option>
		<option value="23" style="word-spacing: 0em;">19. MADHYA PRADESH / मध्य प्रदेश</option>
		<option value="27" style="word-spacing: 0em;">20. MAHARASHTRA / महाराष्ट्र</option>
		<option value="14" style="word-spacing: 0em;">21. MANIPUR / मणिपुर</option>
		<option value="17" style="word-spacing: 0em;">22. MEGHALAYA / मेघालय</option>
		<option value="15" style="word-spacing: 0em;">23. MIZORAM / मिज़ोरम</option>
		<option value="13" style="word-spacing: 0em;">24. NAGALAND / नागालैण्ड</option>
		<option value="21" style="word-spacing: 0em;">25. ODISHA / ओड़िशा</option>
		<option value="34" style="word-spacing: 0em;">26. PUDUCHERRY / पुडुचेरी</option>
		<option value="3" style="word-spacing: 0em;">27. PUNJAB / पंजाब</option>
		<option value="8" style="word-spacing: 0em;">28. RAJASTHAN / राजस्थान</option>
		<option value="11" style="word-spacing: 0em;">29. SIKKIM / सिक्किम</option>
		<option value="33" style="word-spacing: 0em;">30. TAMIL NADU / तमिलनाडु</option>
		<option value="36" style="word-spacing: 0em;">31. TELANGANA / तेलंगाना</option>
		<option value="38" style="word-spacing: 0em;">32. THE DADRA AND NAGAR HAVELI AND DAMAN AND DIU / दादरा और नगर हवेली और दमन और दीव</option>
		<option value="16" style="word-spacing: 0em;">33. TRIPURA / त्रिपुरा</option>
		<option value="9" style="word-spacing: 0em;">34. UTTAR PRADESH / उत्तर प्रदेश</option>
		<option value="5" style="word-spacing: 0em;">35. UTTARAKHAND / उत्तराखण्ड</option>
		<option value="19" style="word-spacing: 0em;">36. WEST BENGAL / पश्चिम बंगाल</option>

	</select>
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator18" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4" style="word-spacing: 0em;">
                                                            <div class="form-group" style="word-spacing: 0em;">
                                                                <label style="word-spacing: 0em;">
                                                                    District
                                                                </label>

                                                                <select name="ctl00$ContentPlaceHolder1$ddlPDistrict" id="ctl00_ContentPlaceHolder1_ddlPDistrict" class="form-control" style="word-spacing: 0em;">
		<option value="0" style="word-spacing: 0em;">Choose District</option>

	</select>
                                                                <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator19" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-12" style="word-spacing: 0em;">
                                                        <input type="submit" name="ctl00$ContentPlaceHolder1$BtnPAdd" value="Add Plant" onclick="javascript:WebForm_DoPostBackWithOptions(new WebForm_PostBackOptions(&quot;ctl00$ContentPlaceHolder1$BtnPAdd&quot;, &quot;&quot;, true, &quot;P&quot;, &quot;&quot;, false, false))" id="ctl00_ContentPlaceHolder1_BtnPAdd" class="btn btn-primary" style="word-spacing: 0em;">
                                                        <br style="word-spacing: 0em;">
                                                        <label style="word-spacing: 0em;">
                                                            <span id="ctl00_ContentPlaceHolder1_lblPlantError" style="color: red; font-weight: bold; word-spacing: 0em;"></span>
                                                        </label>
                                                        <span id="ctl00_ContentPlaceHolder1_CustomValidator2" class="validation-error" style="color: red; display: none; word-spacing: 0em;">Please enter Plant Address detail by click on Add Button</span>
                                                    </div>

                                                    <br style="word-spacing: 0em;">
                                                    <div class="row" style="word-spacing: 0em;">
                                                        <div class="col-md-12" style="word-spacing: 0em;">
                                                            <div style="word-spacing: 0em;">
                                                                <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnRegPlant" id="ctl00_ContentPlaceHolder1_hdnRegPlant" value="1" style="word-spacing: 0em;">
                                                            </div>
                                                            <div class="box-body table-responsive no-padding" style="word-spacing: 0em;">
                                                                <div style="overflow: auto; word-spacing: 0em;">
                                                                    <div style="word-spacing: 0em;">
		<table cellspacing="0" rules="all" class="table table-bordered table-hover table-striped" border="1" id="ctl00_ContentPlaceHolder1_gvPlantUAM" style="width: 100%; border-collapse: collapse; word-spacing: 0em;">
			<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
				<th scope="col" style="word-spacing: 0em;">SNo.</th><th scope="col" style="word-spacing: 0em;">Unit Name</th><th scope="col" style="word-spacing: 0em;">Flat</th><th scope="col" style="word-spacing: 0em;">Building</th><th scope="col" style="word-spacing: 0em;">Road</th><th scope="col" style="word-spacing: 0em;">Village/Town</th><th scope="col" style="word-spacing: 0em;">Block </th><th scope="col" style="word-spacing: 0em;">City</th><th scope="col" style="word-spacing: 0em;">Pin</th><th scope="col" style="word-spacing: 0em;">State</th><th scope="col" style="word-spacing: 0em;">District</th><th scope="col" style="word-spacing: 0em;">Delete</th>
			</tr><tr style="word-spacing: 0em;">
				<td style="width: 5%; word-spacing: 0em;">
                                                                                    <span style="word-spacing: 0em;">
                                                                                        1</span>
                                                                                </td><td style="width: 10%; word-spacing: 0em;">PARVEEN KUMAR</td><td style="width: 5%; word-spacing: 0em;">VILLAGE SHILA KIPPAR</td><td style="width: 10%; word-spacing: 0em;">PO DUDAR</td><td style="width: 10%; word-spacing: 0em;">MANDI</td><td style="width: 10%; word-spacing: 0em;">TEHSIL SADAR MANDI</td><td style="width: 10%; word-spacing: 0em;">SADAR</td><td style="width: 10%; word-spacing: 0em;">MANDI</td><td style="width: 5%; word-spacing: 0em;">175001</td><td style="width: 10%; word-spacing: 0em;">HIMACHAL PRADESH</td><td style="width: 10%; word-spacing: 0em;">MANDI</td><td style="width: 5%; word-spacing: 0em;">
                                                                                    <input type="image" name="ctl00$ContentPlaceHolder1$gvPlantUAM$ctl02$imgbtnPgridDelete" id="ctl00_ContentPlaceHolder1_gvPlantUAM_ctl02_imgbtnPgridDelete" src="../Images/delete.jpg" style="border-width: 0px; word-spacing: 0em;">
                                                                                </td>
			</tr>
		</tbody></table>
	</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="ctl00_ContentPlaceHolder1_div52" class="row" style="word-spacing: 0em;">
                                <div class="col-md-12" style="word-spacing: 0em;">
                                    <div class="card card-secondary" style="word-spacing: 0em;">
                                        <div class="card-header" style="word-spacing: 0em;">
                                            <h3 class="card-title" style="word-spacing: 0em;">National Industrial Classification (NIC) Code Details</h3>
                                        </div>
                                        <div class="card-body" style="word-spacing: 0em;">
                                            <div class="tab-content" style="word-spacing: 0em;">
                                                <div class="tab-pane active" id="org" style="word-spacing: 0em;">
                                                    <div class="form-group" style="word-spacing: 0em;">
                                                        <label for="datepicker" style="word-spacing: 0em;">
                                                            17. Major Activity of Unit / इकाई की प्रमुख गतिविधि
                                                        </label>
                                                        
                                                        <table id="ctl00_ContentPlaceHolder1_rdbCatgg" onchange="resetValidationState(this);" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbCatgg_0" type="radio" name="ctl00$ContentPlaceHolder1$rdbCatgg" value="1" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rdbCatgg$0\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbCatgg_0" style="word-spacing: 0em;">Manufacturing / विनिर्माण</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbCatgg_1" type="radio" name="ctl00$ContentPlaceHolder1$rdbCatgg" value="2" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbCatgg_1" style="word-spacing: 0em;">Services / सेवा</label></td>
		</tr>
	</tbody></table>
                                                        <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    </div>

                                                    <div id="ctl00_ContentPlaceHolder1_divsubcatg" class="form-group" style="word-spacing: 0em;">
                                                        <label for="datepicker" style="word-spacing: 0em;">
                                                            17.1 Major Activity Under Services /  सेवा के तहत प्रमुख गतिविधि
                                                        </label>
                                                        
                                                        <table id="ctl00_ContentPlaceHolder1_rdbSubCategg" onchange="resetValidationState(this);" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbSubCategg_0" type="radio" name="ctl00$ContentPlaceHolder1$rdbSubCategg" value="1" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbSubCategg_0" style="word-spacing: 0em;">Non-Trading / गैर-व्यापारिक</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbSubCategg_1" type="radio" name="ctl00$ContentPlaceHolder1$rdbSubCategg" value="2" checked="checked" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbSubCategg_1" style="word-spacing: 0em;">Trading / व्यापारिक</label></td>
		</tr>
	</tbody></table>
                                                        <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator42" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    </div>
                                                    
                                                    <div class="form-group" id="div1" style="word-spacing: 0em;">
                                                        <label for="datepicker" style="word-spacing: 0em;">
                                                            18. National Industrial Classification (NIC) Code for Activities(One or more activities
                                                                can be added)</label>
                                                    </div>
                                                    
                                                    <div class="form-group" style="word-spacing: 0em;">
                                                        <label style="word-spacing: 0em;">
                                                            Search NIC Code in Lesser Steps (To Avoid 3 Step Selection of NIC Activities)
                                                        </label>
                                                        <input name="ctl00$ContentPlaceHolder1$txtsearchNic" type="text" onchange="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$txtsearchNic\',\'\')', 0)" onkeypress="if (WebForm_TextBoxKeyHandler(event) == false) return false;" id="ctl00_ContentPlaceHolder1_txtsearchNic" class="form-control" placeholder="Search NIC Code" autocomplete="off" style="word-spacing: 0em;">
                                                        <div id="listPlacement" style="height: 250px; overflow: scroll; width: 800px; display: none; visibility: hidden; position: absolute; word-spacing: 0em;" class="AutoExtender">
                                                        </div>
                                                        
                                                        
                                                        <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator24" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    </div>
                                                    <div class="form-group" style="word-spacing: 0em;">
                                                        <table id="ctl00_ContentPlaceHolder1_rdbCatggMultiple" onchange="resetValidationState(this);" border="0" style="word-spacing: 0em;">
		<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
			<td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbCatggMultiple_0" type="radio" name="ctl00$ContentPlaceHolder1$rdbCatggMultiple" value="1" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rdbCatggMultiple$0\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbCatggMultiple_0" style="word-spacing: 0em;">Manufacturing / विनिर्माण</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbCatggMultiple_1" type="radio" name="ctl00$ContentPlaceHolder1$rdbCatggMultiple" value="2" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rdbCatggMultiple$1\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbCatggMultiple_1" style="word-spacing: 0em;">Services / सेवा</label></td><td style="word-spacing: 0em;"><input id="ctl00_ContentPlaceHolder1_rdbCatggMultiple_2" type="radio" name="ctl00$ContentPlaceHolder1$rdbCatggMultiple" value="3" onclick="javascript:setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$rdbCatggMultiple$2\',\'\')', 0)" style="word-spacing: 0em;"><label for="ctl00_ContentPlaceHolder1_rdbCatggMultiple_2" style="word-spacing: 0em;">Trading / व्यापारिक</label></td>
		</tr>
	</tbody></table>
                                                        
                                                        <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                    </div>
                                                    <div class="col-md-12" style="word-spacing: 0em;">
                                                        <div class="row" style="word-spacing: 0em;">
                                                            <div class="col-md-4" style="word-spacing: 0em;">
                                                                <div class="form-group" id="div2digit" style="word-spacing: 0em;">
                                                                    <label for="datepicker" style="word-spacing: 0em;">
                                                                        NIC 2 Digit Code</label>
                                                                    <select name="ctl00$ContentPlaceHolder1$ddl2NicCode" onchange="resetValidationState(this);setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$ddl2NicCode\',\'\')', 0)" id="ctl00_ContentPlaceHolder1_ddl2NicCode" class="form-control" style="word-spacing: 0em;">
		<option selected="selected" value="0" style="word-spacing: 0em;">Choose 2 Digit NIC Code</option>

	</select>
                                                                    
                                                                    <span id="ctl00_ContentPlaceHolder1_RequiredFggieldValidator17" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4" style="word-spacing: 0em;">
                                                                <div class="form-group" id="div4digit" style="word-spacing: 0em;">
                                                                    <label for="datepicker" style="word-spacing: 0em;">
                                                                        NIC 4 Digit Code</label>
                                                                    <select name="ctl00$ContentPlaceHolder1$ddl4NicCode" onchange="resetValidationState(this);setTimeout('__doPostBack(\'ctl00$ContentPlaceHolder1$ddl4NicCode\',\'\')', 0)" id="ctl00_ContentPlaceHolder1_ddl4NicCode" class="form-control" style="word-spacing: 0em;">
		<option selected="selected" value="0" style="word-spacing: 0em;">Choose 4 Digit NIC Code</option>

	</select>
                                                                    
                                                                    <span id="ctl00_ContentPlaceHolder1_RequiredFieldddValidator17" style="color: red; display: none; word-spacing: 0em;">Required</span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4" style="word-spacing: 0em;">
                                                                <div class="form-group" id="div5digit" style="word-spacing: 0em;">
                                                                    <label for="datepicker" style="word-spacing: 0em;">
                                                                        NIC 5 Digit Code</label>

                                                                    <select name="ctl00$ContentPlaceHolder1$ddl5NicCode" id="ctl00_ContentPlaceHolder1_ddl5NicCode" class="form-control" style="word-spacing: 0em;">
		<option value="0" style="word-spacing: 0em;">Choose 5 Digit NIC Code</option>

	</select>
                                                                    
                                                                    <span id="ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7" style="color: red; display: none; word-spacing: 0em;">Required</span>

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group" id="div2" style="word-spacing: 0em;">
                                                        <label for="datepicker" style="word-spacing: 0em;">
                                                        </label>
                                                        <input type="submit" name="ctl00$ContentPlaceHolder1$btnAddMore" value="Add Activity" onclick="javascript:WebForm_DoPostBackWithOptions(new WebForm_PostBackOptions(&quot;ctl00$ContentPlaceHolder1$btnAddMore&quot;, &quot;&quot;, true, &quot;B&quot;, &quot;&quot;, false, false))" id="ctl00_ContentPlaceHolder1_btnAddMore" tabindex="5" class="btn btn-primary" style="word-spacing: 0em;">
                                                        <br style="word-spacing: 0em;">
                                                        <label for="distric" style="word-spacing: 0em;">
                                                            <span id="ctl00_ContentPlaceHolder1_lblMSGError" style="color: red; font-weight: bold; word-spacing: 0em;"></span>
                                                        </label>
                                                        <span id="ctl00_ContentPlaceHolder1_cvRegProduct" class="validation-error" style="color: red; display: none; word-spacing: 0em;">Please enter activity detail by click on Add Button</span>
                                                    </div>
                                                    <div class="form-group" style="word-spacing: 0em;">
                                                        <div style="word-spacing: 0em;">
                                                            <input type="hidden" name="ctl00$ContentPlaceHolder1$hdnRegProduct" id="ctl00_ContentPlaceHolder1_hdnRegProduct" value="1" style="word-spacing: 0em;">
                                                        </div>
                                                        <div class="box-body table-responsive no-padding" style="overflow: auto; word-spacing: 0em;">
                                                            <div style="word-spacing: 0em;">
		<table cellspacing="0" rules="all" class="table table-bordered table-hover table-striped" border="1" id="ctl00_ContentPlaceHolder1_gvBuldingWorkDetailUAM" style="width: 100%; border-collapse: collapse; word-spacing: 0em;">
			<tbody style="word-spacing: 0em;"><tr style="word-spacing: 0em;">
				<th scope="col" style="word-spacing: 0em;">SNo.</th><th scope="col" style="word-spacing: 0em;">NIC 2 Digit</th><th scope="col" style="word-spacing: 0em;">NIC 4 Digit</th><th scope="col" style="word-spacing: 0em;">NIC 5 Digit</th><th scope="col" style="word-spacing: 0em;">Activity Type</th><th scope="col" style="word-spacing: 0em;">Delete</th>
			</tr><tr style="word-spacing: 0em;">
				<td style="word-spacing: 0em;">
                                                                            <span style="word-spacing: 0em;">
                                                                                1</span>
                                                                        </td><td style="word-spacing: 0em;">77-Rental and leasing activities</td><td style="word-spacing: 0em;">7710-Renting and leasing of motor vehicles</td><td style="word-spacing: 0em;">77100-Renting and leasing of motor vehicles</td><td style="word-spacing: 0em;">Services</td><td style="word-spacing: 0em;">
                                                                            <input type="image" name="ctl00$ContentPlaceHolder1$gvBuldingWorkDetailUAM$ctl02$imgbtnOfficeBearerAppQrgDelete" id="ctl00_ContentPlaceHolder1_gvBuldingWorkDetailUAM_ctl02_imgbtnOfficeBearerAppQrgDelete" src="../Images/delete.jpg" style="border-width: 0px; word-spacing: 0em;">
                                                                        </td>
			</tr>
		</tbody></table>
	</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12" style="word-spacing: 0em;">

                                    <div class="form-group" style="word-spacing: 0em;">

                                        <input type="submit" name="ctl00$ContentPlaceHolder1$btn_finalsubmit" value="Update details" onclick="javascript:return ConfirmSubmitfinal();" id="ctl00_ContentPlaceHolder1_btn_finalsubmit" class="btn btn-primary" style="word-spacing: 0em;">

                                        <br style="word-spacing: 0em;">
                                        <span id="ctl00_ContentPlaceHolder1_lblMssgg" style="color: red; font-size: larger; font-weight: bold; word-spacing: 0em;"></span>
                                    </div>
                                    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js" style="word-spacing: 0em;"></script>
                                    <script type="text/javascript" style="word-spacing: 0em;">
                                        $(window).on('beforeunload', function () {
                                            $("input[type=submit], input[type=button]").prop("disabled", "disabled");
                                        });
                                    </script>
                                </div>
                            </div>




                        </div>




                        <div id="ctl00_ContentPlaceHolder1_UpdateProgress4" style="display: none; word-spacing: 0em;" role="status" aria-hidden="true">
		
                                
                            
	</div>
                    
</div>
                <!-- /.form div 2 -->

            </div>

        </section>

    </main>
    <!-- End #main -->
    <script src="../js/jquery-1.9.1.min.js" style="word-spacing: 0em;"></script>
    <script type="text/javascript" style="word-spacing: 0em;">



        function EmailAvailability() { //This function call on text change.             
            $.ajax({
                type: "POST",
                url: "Udyam_UpdateNew.aspx/CheckEmail", // this for calling the web method function in cs code.  
                data: '{email: "' + $("#ctl00_ContentPlaceHolder1_txtemail")[0].value + '" }', //  email value  
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: OnSuccessEmail,
                failure: function (response) {
                    alert(response);
                }
            });
        }

        // function OnSuccess  
        function OnSuccessEmail(response) {
            var msg = $("#ctl00_ContentPlaceHolder1_lblEmail")[0];
            switch (response.d) {
                case "true":
                    msg.style.display = "block";
                    msg.style.color = "green";
                    msg.innerHTML = "";
                    break;
                case "false":
                    msg.style.display = "block";
                    msg.style.color = "red";
                    msg.innerHTML = "Registration with this Email has already done 5 times.";
                    $("#ctl00_ContentPlaceHolder1_txtemail")[0].value = "";
                    $("#ctl00_ContentPlaceHolder1_txtemail")[0].focus();
                    break;
            }
        }

        function MobileAvailability() { //This function call on text change.             
            $.ajax({
                type: "POST",
                url: "Udyam_UpdateNew.aspx/CheckMobile", // this for calling the web method function in cs code.  
                data: '{mobile: "' + $("#ctl00_ContentPlaceHolder1_txtmobile")[0].value + '" }', //  email value  
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: OnSuccessMobile,
                failure: function (response) {
                    alert(response);
                }
            });
        }

        // function OnSuccess  
        function OnSuccessMobile(response) {
            var msg = $("#ctl00_ContentPlaceHolder1_lblMobile")[0];
            switch (response.d) {
                case "true":
                    msg.style.display = "block";
                    msg.style.color = "green";
                    msg.innerHTML = "";
                    break;
                case "false":
                    msg.style.display = "block";
                    msg.style.color = "red";
                    msg.innerHTML = "Registration with this Mobile No. has already done 5 times.";
                    $("#ctl00_ContentPlaceHolder1_txtmobile")[0].value = "";
                    $("#ctl00_ContentPlaceHolder1_txtmobile")[0].focus();
                    break;
            }
        }



    </script>

    <script src="/js/jquery-ui.js" type="text/javascript" style="word-spacing: 0em;"></script>
    <link href="js/jquery-ui.css" rel="stylesheet" type="text/css" style="word-spacing: 0em;">
    <script type="text/javascript" style="word-spacing: 0em;">
        $(function () {
            $("[id$=txtdateIncorporation]").datepicker({
                dateFormat: 'dd/mm/yy'
                //              showOn: 'button',
                //            buttonImageOnly: true,
                //            buttonImage: 'calendar.png'
            });

            $("[id$=txtcommencedate]").datepicker({
                dateFormat: 'dd/mm/yy'
                //              showOn: 'button',
                //            buttonImageOnly: true,
                //            buttonImage: 'calendar.png'
            });
        });
        function caldatefuncation() {

            $("[id$=txtdateIncorporation]").datepicker({
                dateFormat: 'dd/mm/yy'
                //              showOn: 'button',
                //            buttonImageOnly: true,
                //            buttonImage: 'calendar.png'
            });

            $("[id$=txtcommencedate]").datepicker({
                dateFormat: 'dd/mm/yy'
                //              showOn: 'button',
                //            buttonImageOnly: true,
                //            buttonImage: 'calendar.png'
            });

        }
    </script>

    </div>
    
<script type="text/javascript" style="word-spacing: 0em;">
//<![CDATA[
var Page_Validators =  new Array(document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator3"), document.getElementById("ctl00_ContentPlaceHolder1_rfvpannumber"), document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator5"), document.getElementById("ctl00_ContentPlaceHolder1_rfvPreviousYearITR"), document.getElementById("ctl00_ContentPlaceHolder1_rfvWhetherGstn"), document.getElementById("ctl00_ContentPlaceHolder1_cvGSTN"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator47"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator14"), document.getElementById("ctl00_ContentPlaceHolder1_revMobile"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator15"), document.getElementById("ctl00_ContentPlaceHolder1_revEmail"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator16"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator8"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator17"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator1"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator30"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator31"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator32"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator33"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator36"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator34"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator4"), document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator1"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator5"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator6"), document.getElementById("ctl00_ContentPlaceHolder1_reqlatude"), document.getElementById("ctl00_ContentPlaceHolder1_reqlngt"), document.getElementById("ctl00_ContentPlaceHolder1_RFVPreviousNumber"), document.getElementById("ctl00_ContentPlaceHolder1_CustomValidator4"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator37"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator38"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator35"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator13"), document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator4"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator11"), document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator3"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator39"), document.getElementById("ctl00_ContentPlaceHolder1_RangeValidator1"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator40"), document.getElementById("ctl00_ContentPlaceHolder1_RangeValidator2"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator41"), document.getElementById("ctl00_ContentPlaceHolder1_RangeValidator3"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator9"), document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator2"), document.getElementById("ctl00_ContentPlaceHolder1_Range1"), document.getElementById("ctl00_ContentPlaceHolder1_CustomValidator1"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator44"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator46"), document.getElementById("ctl00_ContentPlaceHolder1_rfvNCS"), document.getElementById("ctl00_ContentPlaceHolder1_rfvNSIC"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator12"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator312"), document.getElementById("ctl00_ContentPlaceHolder1_rfvUnitName"), document.getElementById("ctl00_ContentPlaceHolder1_rfvddlUnitName"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator20"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator21"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator49"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator23"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator22"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator25"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator26"), document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator6"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator18"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator19"), document.getElementById("ctl00_ContentPlaceHolder1_CustomValidator2"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator42"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator24"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFggieldValidator17"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldddValidator17"), document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7"), document.getElementById("ctl00_ContentPlaceHolder1_cvRegProduct"));
//]]>
</script>

<script type="text/javascript" style="word-spacing: 0em;">
//<![CDATA[
var ctl00_ContentPlaceHolder1_RequiredFieldValidator3 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator3"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator3");
ctl00_ContentPlaceHolder1_RequiredFieldValidator3.controltovalidate = "ctl00_ContentPlaceHolder1_rbpanyesno";
ctl00_ContentPlaceHolder1_RequiredFieldValidator3.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator3.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator3.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator3.validationGroup = "XP";
ctl00_ContentPlaceHolder1_RequiredFieldValidator3.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator3.initialvalue = "";
var ctl00_ContentPlaceHolder1_rfvpannumber = document.all ? document.all["ctl00_ContentPlaceHolder1_rfvpannumber"] : document.getElementById("ctl00_ContentPlaceHolder1_rfvpannumber");
ctl00_ContentPlaceHolder1_rfvpannumber.controltovalidate = "ctl00_ContentPlaceHolder1_txtPan";
ctl00_ContentPlaceHolder1_rfvpannumber.errormessage = "Required";
ctl00_ContentPlaceHolder1_rfvpannumber.display = "Dynamic";
ctl00_ContentPlaceHolder1_rfvpannumber.validationGroup = "XP";
ctl00_ContentPlaceHolder1_rfvpannumber.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_rfvpannumber.initialvalue = "";
var ctl00_ContentPlaceHolder1_RegularExpressionValidator5 = document.all ? document.all["ctl00_ContentPlaceHolder1_RegularExpressionValidator5"] : document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator5");
ctl00_ContentPlaceHolder1_RegularExpressionValidator5.controltovalidate = "ctl00_ContentPlaceHolder1_txtPan";
ctl00_ContentPlaceHolder1_RegularExpressionValidator5.focusOnError = "t";
ctl00_ContentPlaceHolder1_RegularExpressionValidator5.errormessage = "Invalid PAN Card";
ctl00_ContentPlaceHolder1_RegularExpressionValidator5.display = "Dynamic";
ctl00_ContentPlaceHolder1_RegularExpressionValidator5.validationGroup = "XP";
ctl00_ContentPlaceHolder1_RegularExpressionValidator5.evaluationfunction = "RegularExpressionValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RegularExpressionValidator5.validationexpression = "[(a-z)(A-Z)]{5}\\d{4}[(a-z)(A-Z)]{1}";
var ctl00_ContentPlaceHolder1_rfvPreviousYearITR = document.all ? document.all["ctl00_ContentPlaceHolder1_rfvPreviousYearITR"] : document.getElementById("ctl00_ContentPlaceHolder1_rfvPreviousYearITR");
ctl00_ContentPlaceHolder1_rfvPreviousYearITR.controltovalidate = "ctl00_ContentPlaceHolder1_rblPreviousYearITR";
ctl00_ContentPlaceHolder1_rfvPreviousYearITR.focusOnError = "t";
ctl00_ContentPlaceHolder1_rfvPreviousYearITR.errormessage = "Required";
ctl00_ContentPlaceHolder1_rfvPreviousYearITR.display = "Dynamic";
ctl00_ContentPlaceHolder1_rfvPreviousYearITR.validationGroup = "Y";
ctl00_ContentPlaceHolder1_rfvPreviousYearITR.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_rfvPreviousYearITR.initialvalue = "";
var ctl00_ContentPlaceHolder1_rfvWhetherGstn = document.all ? document.all["ctl00_ContentPlaceHolder1_rfvWhetherGstn"] : document.getElementById("ctl00_ContentPlaceHolder1_rfvWhetherGstn");
ctl00_ContentPlaceHolder1_rfvWhetherGstn.controltovalidate = "ctl00_ContentPlaceHolder1_rblWhetherGstn";
ctl00_ContentPlaceHolder1_rfvWhetherGstn.focusOnError = "t";
ctl00_ContentPlaceHolder1_rfvWhetherGstn.errormessage = "Required";
ctl00_ContentPlaceHolder1_rfvWhetherGstn.display = "Dynamic";
ctl00_ContentPlaceHolder1_rfvWhetherGstn.validationGroup = "Y";
ctl00_ContentPlaceHolder1_rfvWhetherGstn.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_rfvWhetherGstn.initialvalue = "";
var ctl00_ContentPlaceHolder1_cvGSTN = document.all ? document.all["ctl00_ContentPlaceHolder1_cvGSTN"] : document.getElementById("ctl00_ContentPlaceHolder1_cvGSTN");
ctl00_ContentPlaceHolder1_cvGSTN.controltovalidate = "ctl00_ContentPlaceHolder1_rblWhetherGstn";
ctl00_ContentPlaceHolder1_cvGSTN.focusOnError = "t";
ctl00_ContentPlaceHolder1_cvGSTN.errormessage = "GSTIN is mandetory subject to the provision of CGST Act 2017 and as notified by the ministry of MSME <a href=\'https://udyamregistration.gov.in/docs/225669.pdf\' target=\'_blank\'>vide S.O. 1055(E) dated 05th March 2021</a>. You are advised to apply for GSTIN suitably to avoid any inconvenience.";
ctl00_ContentPlaceHolder1_cvGSTN.display = "Dynamic";
ctl00_ContentPlaceHolder1_cvGSTN.enabled = "False";
ctl00_ContentPlaceHolder1_cvGSTN.validationGroup = "Y";
ctl00_ContentPlaceHolder1_cvGSTN.evaluationfunction = "CustomValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_cvGSTN.clientvalidationfunction = "ValidateTurnover";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator47 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator47"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator47");
ctl00_ContentPlaceHolder1_RequiredFieldValidator47.controltovalidate = "ctl00_ContentPlaceHolder1_txtOwnernamePan";
ctl00_ContentPlaceHolder1_RequiredFieldValidator47.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator47.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator47.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator47.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator47.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator47.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator14 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator14"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator14");
ctl00_ContentPlaceHolder1_RequiredFieldValidator14.controltovalidate = "ctl00_ContentPlaceHolder1_txtmobile";
ctl00_ContentPlaceHolder1_RequiredFieldValidator14.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator14.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator14.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator14.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator14.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator14.initialvalue = "";
var ctl00_ContentPlaceHolder1_revMobile = document.all ? document.all["ctl00_ContentPlaceHolder1_revMobile"] : document.getElementById("ctl00_ContentPlaceHolder1_revMobile");
ctl00_ContentPlaceHolder1_revMobile.controltovalidate = "ctl00_ContentPlaceHolder1_txtmobile";
ctl00_ContentPlaceHolder1_revMobile.focusOnError = "t";
ctl00_ContentPlaceHolder1_revMobile.errormessage = "Mobile no. should be of 10 digits must start with 9, 8, 7 or 6";
ctl00_ContentPlaceHolder1_revMobile.display = "Dynamic";
ctl00_ContentPlaceHolder1_revMobile.validationGroup = "Y";
ctl00_ContentPlaceHolder1_revMobile.evaluationfunction = "RegularExpressionValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_revMobile.validationexpression = "(9|8|7|6)\\d{9}";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator15 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator15"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator15");
ctl00_ContentPlaceHolder1_RequiredFieldValidator15.controltovalidate = "ctl00_ContentPlaceHolder1_txtemail";
ctl00_ContentPlaceHolder1_RequiredFieldValidator15.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator15.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator15.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator15.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator15.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator15.initialvalue = "";
var ctl00_ContentPlaceHolder1_revEmail = document.all ? document.all["ctl00_ContentPlaceHolder1_revEmail"] : document.getElementById("ctl00_ContentPlaceHolder1_revEmail");
ctl00_ContentPlaceHolder1_revEmail.controltovalidate = "ctl00_ContentPlaceHolder1_txtemail";
ctl00_ContentPlaceHolder1_revEmail.focusOnError = "t";
ctl00_ContentPlaceHolder1_revEmail.errormessage = "Invalid email";
ctl00_ContentPlaceHolder1_revEmail.display = "Dynamic";
ctl00_ContentPlaceHolder1_revEmail.validationGroup = "Y";
ctl00_ContentPlaceHolder1_revEmail.evaluationfunction = "RegularExpressionValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_revEmail.validationexpression = "\\w+([-+.\']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator16 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator16"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator16");
ctl00_ContentPlaceHolder1_RequiredFieldValidator16.controltovalidate = "ctl00_ContentPlaceHolder1_rdbcategory";
ctl00_ContentPlaceHolder1_RequiredFieldValidator16.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator16.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator16.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator16.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator16.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator16.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator8 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator8"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator8");
ctl00_ContentPlaceHolder1_RequiredFieldValidator8.controltovalidate = "ctl00_ContentPlaceHolder1_rbtGender";
ctl00_ContentPlaceHolder1_RequiredFieldValidator8.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator8.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator8.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator8.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator8.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator8.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator17 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator17"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator17");
ctl00_ContentPlaceHolder1_RequiredFieldValidator17.controltovalidate = "ctl00_ContentPlaceHolder1_rbtPh";
ctl00_ContentPlaceHolder1_RequiredFieldValidator17.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator17.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator17.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator17.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator17.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator17.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator1 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator1"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator1");
ctl00_ContentPlaceHolder1_RequiredFieldValidator1.controltovalidate = "ctl00_ContentPlaceHolder1_txtenterprisename";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator30 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator30"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator30");
ctl00_ContentPlaceHolder1_RequiredFieldValidator30.controltovalidate = "ctl00_ContentPlaceHolder1_txtOffFlatNo";
ctl00_ContentPlaceHolder1_RequiredFieldValidator30.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator30.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator30.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator30.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator30.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator30.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator31 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator31"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator31");
ctl00_ContentPlaceHolder1_RequiredFieldValidator31.controltovalidate = "ctl00_ContentPlaceHolder1_txtOffBuilding";
ctl00_ContentPlaceHolder1_RequiredFieldValidator31.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator31.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator31.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator31.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator31.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator31.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator32 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator32"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator32");
ctl00_ContentPlaceHolder1_RequiredFieldValidator32.controltovalidate = "ctl00_ContentPlaceHolder1_txtOffVillageTown";
ctl00_ContentPlaceHolder1_RequiredFieldValidator32.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator32.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator32.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator32.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator32.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator32.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator33 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator33"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator33");
ctl00_ContentPlaceHolder1_RequiredFieldValidator33.controltovalidate = "ctl00_ContentPlaceHolder1_txtOffBlock";
ctl00_ContentPlaceHolder1_RequiredFieldValidator33.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator33.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator33.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator33.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator33.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator33.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator36 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator36"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator36");
ctl00_ContentPlaceHolder1_RequiredFieldValidator36.controltovalidate = "ctl00_ContentPlaceHolder1_txtOffRoadStreetLane";
ctl00_ContentPlaceHolder1_RequiredFieldValidator36.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator36.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator36.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator36.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator36.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator36.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator34 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator34"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator34");
ctl00_ContentPlaceHolder1_RequiredFieldValidator34.controltovalidate = "ctl00_ContentPlaceHolder1_txtOffCity";
ctl00_ContentPlaceHolder1_RequiredFieldValidator34.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator34.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator34.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator34.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator34.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator34.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator4 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator4"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator4");
ctl00_ContentPlaceHolder1_RequiredFieldValidator4.controltovalidate = "ctl00_ContentPlaceHolder1_txtOffPin";
ctl00_ContentPlaceHolder1_RequiredFieldValidator4.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator4.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator4.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator4.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator4.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator4.initialvalue = "";
var ctl00_ContentPlaceHolder1_RegularExpressionValidator1 = document.all ? document.all["ctl00_ContentPlaceHolder1_RegularExpressionValidator1"] : document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator1");
ctl00_ContentPlaceHolder1_RegularExpressionValidator1.controltovalidate = "ctl00_ContentPlaceHolder1_txtOffPin";
ctl00_ContentPlaceHolder1_RegularExpressionValidator1.focusOnError = "t";
ctl00_ContentPlaceHolder1_RegularExpressionValidator1.errormessage = "Pin Code must be of 6 digit and not start with 0 and 9";
ctl00_ContentPlaceHolder1_RegularExpressionValidator1.display = "Dynamic";
ctl00_ContentPlaceHolder1_RegularExpressionValidator1.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RegularExpressionValidator1.evaluationfunction = "RegularExpressionValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RegularExpressionValidator1.validationexpression = "^([1-8])([0-9]){5}$";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator5 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator5"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator5");
ctl00_ContentPlaceHolder1_RequiredFieldValidator5.controltovalidate = "ctl00_ContentPlaceHolder1_ddlstate";
ctl00_ContentPlaceHolder1_RequiredFieldValidator5.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator5.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator5.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator5.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator5.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator5.initialvalue = "0";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator6 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator6"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator6");
ctl00_ContentPlaceHolder1_RequiredFieldValidator6.controltovalidate = "ctl00_ContentPlaceHolder1_ddlDistrict";
ctl00_ContentPlaceHolder1_RequiredFieldValidator6.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator6.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator6.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator6.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator6.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator6.initialvalue = "0";
var ctl00_ContentPlaceHolder1_reqlatude = document.all ? document.all["ctl00_ContentPlaceHolder1_reqlatude"] : document.getElementById("ctl00_ContentPlaceHolder1_reqlatude");
ctl00_ContentPlaceHolder1_reqlatude.controltovalidate = "ctl00_ContentPlaceHolder1_txtLat";
ctl00_ContentPlaceHolder1_reqlatude.focusOnError = "t";
ctl00_ContentPlaceHolder1_reqlatude.errormessage = "Required";
ctl00_ContentPlaceHolder1_reqlatude.display = "Dynamic";
ctl00_ContentPlaceHolder1_reqlatude.validationGroup = "Y";
ctl00_ContentPlaceHolder1_reqlatude.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_reqlatude.initialvalue = "";
var ctl00_ContentPlaceHolder1_reqlngt = document.all ? document.all["ctl00_ContentPlaceHolder1_reqlngt"] : document.getElementById("ctl00_ContentPlaceHolder1_reqlngt");
ctl00_ContentPlaceHolder1_reqlngt.controltovalidate = "ctl00_ContentPlaceHolder1_txtLngt";
ctl00_ContentPlaceHolder1_reqlngt.focusOnError = "t";
ctl00_ContentPlaceHolder1_reqlngt.errormessage = "Required";
ctl00_ContentPlaceHolder1_reqlngt.display = "Dynamic";
ctl00_ContentPlaceHolder1_reqlngt.validationGroup = "Y";
ctl00_ContentPlaceHolder1_reqlngt.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_reqlngt.initialvalue = "";
var ctl00_ContentPlaceHolder1_RFVPreviousNumber = document.all ? document.all["ctl00_ContentPlaceHolder1_RFVPreviousNumber"] : document.getElementById("ctl00_ContentPlaceHolder1_RFVPreviousNumber");
ctl00_ContentPlaceHolder1_RFVPreviousNumber.controltovalidate = "ctl00_ContentPlaceHolder1_txtPreviousNumber";
ctl00_ContentPlaceHolder1_RFVPreviousNumber.focusOnError = "t";
ctl00_ContentPlaceHolder1_RFVPreviousNumber.errormessage = "Required";
ctl00_ContentPlaceHolder1_RFVPreviousNumber.display = "Dynamic";
ctl00_ContentPlaceHolder1_RFVPreviousNumber.enabled = "False";
ctl00_ContentPlaceHolder1_RFVPreviousNumber.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RFVPreviousNumber.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RFVPreviousNumber.initialvalue = "";
var ctl00_ContentPlaceHolder1_CustomValidator4 = document.all ? document.all["ctl00_ContentPlaceHolder1_CustomValidator4"] : document.getElementById("ctl00_ContentPlaceHolder1_CustomValidator4");
ctl00_ContentPlaceHolder1_CustomValidator4.controltovalidate = "ctl00_ContentPlaceHolder1_txtdateIncorporation";
ctl00_ContentPlaceHolder1_CustomValidator4.focusOnError = "t";
ctl00_ContentPlaceHolder1_CustomValidator4.errormessage = "Invalid date";
ctl00_ContentPlaceHolder1_CustomValidator4.display = "Dynamic";
ctl00_ContentPlaceHolder1_CustomValidator4.validationGroup = "Y";
ctl00_ContentPlaceHolder1_CustomValidator4.evaluationfunction = "CustomValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_CustomValidator4.clientvalidationfunction = "ValidateDate";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator37 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator37"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator37");
ctl00_ContentPlaceHolder1_RequiredFieldValidator37.controltovalidate = "ctl00_ContentPlaceHolder1_txtdateIncorporation";
ctl00_ContentPlaceHolder1_RequiredFieldValidator37.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator37.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator37.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator37.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator37.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator37.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator38 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator38"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator38");
ctl00_ContentPlaceHolder1_RequiredFieldValidator38.controltovalidate = "ctl00_ContentPlaceHolder1_rblcommenced";
ctl00_ContentPlaceHolder1_RequiredFieldValidator38.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator38.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator38.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator38.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator38.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator38.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator35 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator35"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator35");
ctl00_ContentPlaceHolder1_RequiredFieldValidator35.controltovalidate = "ctl00_ContentPlaceHolder1_txtBankName";
ctl00_ContentPlaceHolder1_RequiredFieldValidator35.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator35.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator35.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator35.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator35.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator35.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator13 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator13"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator13");
ctl00_ContentPlaceHolder1_RequiredFieldValidator13.controltovalidate = "ctl00_ContentPlaceHolder1_txtifsccode";
ctl00_ContentPlaceHolder1_RequiredFieldValidator13.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator13.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator13.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator13.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator13.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator13.initialvalue = "";
var ctl00_ContentPlaceHolder1_RegularExpressionValidator4 = document.all ? document.all["ctl00_ContentPlaceHolder1_RegularExpressionValidator4"] : document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator4");
ctl00_ContentPlaceHolder1_RegularExpressionValidator4.controltovalidate = "ctl00_ContentPlaceHolder1_txtifsccode";
ctl00_ContentPlaceHolder1_RegularExpressionValidator4.focusOnError = "t";
ctl00_ContentPlaceHolder1_RegularExpressionValidator4.errormessage = "IFS code must be of first 4 of alphabets after that 0  and last 6 of alpha-numeric ";
ctl00_ContentPlaceHolder1_RegularExpressionValidator4.display = "Dynamic";
ctl00_ContentPlaceHolder1_RegularExpressionValidator4.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RegularExpressionValidator4.evaluationfunction = "RegularExpressionValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RegularExpressionValidator4.validationexpression = "[A-Z|a-z]{4}[0][a-z|A-Z|0-9]{6}$";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator11 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator11"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator11");
ctl00_ContentPlaceHolder1_RequiredFieldValidator11.controltovalidate = "ctl00_ContentPlaceHolder1_txtaccountno";
ctl00_ContentPlaceHolder1_RequiredFieldValidator11.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator11.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator11.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator11.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator11.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator11.initialvalue = "";
var ctl00_ContentPlaceHolder1_RegularExpressionValidator3 = document.all ? document.all["ctl00_ContentPlaceHolder1_RegularExpressionValidator3"] : document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator3");
ctl00_ContentPlaceHolder1_RegularExpressionValidator3.controltovalidate = "ctl00_ContentPlaceHolder1_txtaccountno";
ctl00_ContentPlaceHolder1_RegularExpressionValidator3.focusOnError = "t";
ctl00_ContentPlaceHolder1_RegularExpressionValidator3.errormessage = "Please enter numbers only";
ctl00_ContentPlaceHolder1_RegularExpressionValidator3.display = "Dynamic";
ctl00_ContentPlaceHolder1_RegularExpressionValidator3.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RegularExpressionValidator3.evaluationfunction = "RegularExpressionValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RegularExpressionValidator3.validationexpression = "\\d+";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator39 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator39"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator39");
ctl00_ContentPlaceHolder1_RequiredFieldValidator39.controltovalidate = "ctl00_ContentPlaceHolder1_txtNoofpersonMale";
ctl00_ContentPlaceHolder1_RequiredFieldValidator39.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator39.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator39.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator39.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator39.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator39.initialvalue = "";
var ctl00_ContentPlaceHolder1_RangeValidator1 = document.all ? document.all["ctl00_ContentPlaceHolder1_RangeValidator1"] : document.getElementById("ctl00_ContentPlaceHolder1_RangeValidator1");
ctl00_ContentPlaceHolder1_RangeValidator1.controltovalidate = "ctl00_ContentPlaceHolder1_txtNoofpersonMale";
ctl00_ContentPlaceHolder1_RangeValidator1.focusOnError = "t";
ctl00_ContentPlaceHolder1_RangeValidator1.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RangeValidator1.type = "Integer";
ctl00_ContentPlaceHolder1_RangeValidator1.evaluationfunction = "RangeValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RangeValidator1.maximumvalue = "6000";
ctl00_ContentPlaceHolder1_RangeValidator1.minimumvalue = "0";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator40 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator40"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator40");
ctl00_ContentPlaceHolder1_RequiredFieldValidator40.controltovalidate = "ctl00_ContentPlaceHolder1_txtNoofpersonFemale";
ctl00_ContentPlaceHolder1_RequiredFieldValidator40.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator40.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator40.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator40.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator40.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator40.initialvalue = "";
var ctl00_ContentPlaceHolder1_RangeValidator2 = document.all ? document.all["ctl00_ContentPlaceHolder1_RangeValidator2"] : document.getElementById("ctl00_ContentPlaceHolder1_RangeValidator2");
ctl00_ContentPlaceHolder1_RangeValidator2.controltovalidate = "ctl00_ContentPlaceHolder1_txtNoofpersonFemale";
ctl00_ContentPlaceHolder1_RangeValidator2.focusOnError = "t";
ctl00_ContentPlaceHolder1_RangeValidator2.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RangeValidator2.type = "Integer";
ctl00_ContentPlaceHolder1_RangeValidator2.evaluationfunction = "RangeValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RangeValidator2.maximumvalue = "6000";
ctl00_ContentPlaceHolder1_RangeValidator2.minimumvalue = "0";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator41 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator41"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator41");
ctl00_ContentPlaceHolder1_RequiredFieldValidator41.controltovalidate = "ctl00_ContentPlaceHolder1_txtNoofpersonOthers";
ctl00_ContentPlaceHolder1_RequiredFieldValidator41.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator41.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator41.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator41.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator41.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator41.initialvalue = "";
var ctl00_ContentPlaceHolder1_RangeValidator3 = document.all ? document.all["ctl00_ContentPlaceHolder1_RangeValidator3"] : document.getElementById("ctl00_ContentPlaceHolder1_RangeValidator3");
ctl00_ContentPlaceHolder1_RangeValidator3.controltovalidate = "ctl00_ContentPlaceHolder1_txtNoofpersonOthers";
ctl00_ContentPlaceHolder1_RangeValidator3.focusOnError = "t";
ctl00_ContentPlaceHolder1_RangeValidator3.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RangeValidator3.type = "Integer";
ctl00_ContentPlaceHolder1_RangeValidator3.evaluationfunction = "RangeValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RangeValidator3.maximumvalue = "6000";
ctl00_ContentPlaceHolder1_RangeValidator3.minimumvalue = "0";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator9 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator9"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator9");
ctl00_ContentPlaceHolder1_RequiredFieldValidator9.controltovalidate = "ctl00_ContentPlaceHolder1_txttotalemp";
ctl00_ContentPlaceHolder1_RequiredFieldValidator9.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator9.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator9.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator9.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator9.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator9.initialvalue = "";
var ctl00_ContentPlaceHolder1_RegularExpressionValidator2 = document.all ? document.all["ctl00_ContentPlaceHolder1_RegularExpressionValidator2"] : document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator2");
ctl00_ContentPlaceHolder1_RegularExpressionValidator2.controltovalidate = "ctl00_ContentPlaceHolder1_txttotalemp";
ctl00_ContentPlaceHolder1_RegularExpressionValidator2.focusOnError = "t";
ctl00_ContentPlaceHolder1_RegularExpressionValidator2.errormessage = "Please enter numbers only";
ctl00_ContentPlaceHolder1_RegularExpressionValidator2.display = "Dynamic";
ctl00_ContentPlaceHolder1_RegularExpressionValidator2.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RegularExpressionValidator2.evaluationfunction = "RegularExpressionValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RegularExpressionValidator2.validationexpression = "\\d+";
var ctl00_ContentPlaceHolder1_Range1 = document.all ? document.all["ctl00_ContentPlaceHolder1_Range1"] : document.getElementById("ctl00_ContentPlaceHolder1_Range1");
ctl00_ContentPlaceHolder1_Range1.controltovalidate = "ctl00_ContentPlaceHolder1_txttotalemp";
ctl00_ContentPlaceHolder1_Range1.focusOnError = "t";
ctl00_ContentPlaceHolder1_Range1.validationGroup = "Y";
ctl00_ContentPlaceHolder1_Range1.type = "Integer";
ctl00_ContentPlaceHolder1_Range1.evaluationfunction = "RangeValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_Range1.maximumvalue = "18000";
ctl00_ContentPlaceHolder1_Range1.minimumvalue = "1";
var ctl00_ContentPlaceHolder1_CustomValidator1 = document.all ? document.all["ctl00_ContentPlaceHolder1_CustomValidator1"] : document.getElementById("ctl00_ContentPlaceHolder1_CustomValidator1");
ctl00_ContentPlaceHolder1_CustomValidator1.focusOnError = "t";
ctl00_ContentPlaceHolder1_CustomValidator1.errormessage = "You must Agree Declerations.";
ctl00_ContentPlaceHolder1_CustomValidator1.display = "Dynamic";
ctl00_ContentPlaceHolder1_CustomValidator1.validationGroup = "Y";
ctl00_ContentPlaceHolder1_CustomValidator1.evaluationfunction = "CustomValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_CustomValidator1.clientvalidationfunction = "checkAgreement";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator44 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator44"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator44");
ctl00_ContentPlaceHolder1_RequiredFieldValidator44.controltovalidate = "ctl00_ContentPlaceHolder1_rblGeM";
ctl00_ContentPlaceHolder1_RequiredFieldValidator44.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator44.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator44.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator44.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator44.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator44.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator46 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator46"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator46");
ctl00_ContentPlaceHolder1_RequiredFieldValidator46.controltovalidate = "ctl00_ContentPlaceHolder1_rblTReDS";
ctl00_ContentPlaceHolder1_RequiredFieldValidator46.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator46.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator46.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator46.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator46.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator46.initialvalue = "";
var ctl00_ContentPlaceHolder1_rfvNCS = document.all ? document.all["ctl00_ContentPlaceHolder1_rfvNCS"] : document.getElementById("ctl00_ContentPlaceHolder1_rfvNCS");
ctl00_ContentPlaceHolder1_rfvNCS.controltovalidate = "ctl00_ContentPlaceHolder1_rblNCS";
ctl00_ContentPlaceHolder1_rfvNCS.focusOnError = "t";
ctl00_ContentPlaceHolder1_rfvNCS.errormessage = "Required";
ctl00_ContentPlaceHolder1_rfvNCS.display = "Dynamic";
ctl00_ContentPlaceHolder1_rfvNCS.validationGroup = "Y";
ctl00_ContentPlaceHolder1_rfvNCS.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_rfvNCS.initialvalue = "";
var ctl00_ContentPlaceHolder1_rfvNSIC = document.all ? document.all["ctl00_ContentPlaceHolder1_rfvNSIC"] : document.getElementById("ctl00_ContentPlaceHolder1_rfvNSIC");
ctl00_ContentPlaceHolder1_rfvNSIC.controltovalidate = "ctl00_ContentPlaceHolder1_rblnsic";
ctl00_ContentPlaceHolder1_rfvNSIC.focusOnError = "t";
ctl00_ContentPlaceHolder1_rfvNSIC.errormessage = "Required";
ctl00_ContentPlaceHolder1_rfvNSIC.display = "Dynamic";
ctl00_ContentPlaceHolder1_rfvNSIC.validationGroup = "Y";
ctl00_ContentPlaceHolder1_rfvNSIC.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_rfvNSIC.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator12 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator12"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator12");
ctl00_ContentPlaceHolder1_RequiredFieldValidator12.controltovalidate = "ctl00_ContentPlaceHolder1_rblsid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator12.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator12.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator12.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator12.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator12.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator12.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator312 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator312"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator312");
ctl00_ContentPlaceHolder1_RequiredFieldValidator312.controltovalidate = "ctl00_ContentPlaceHolder1_ddlDIC";
ctl00_ContentPlaceHolder1_RequiredFieldValidator312.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator312.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator312.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator312.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator312.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator312.initialvalue = "0";
var ctl00_ContentPlaceHolder1_rfvUnitName = document.all ? document.all["ctl00_ContentPlaceHolder1_rfvUnitName"] : document.getElementById("ctl00_ContentPlaceHolder1_rfvUnitName");
ctl00_ContentPlaceHolder1_rfvUnitName.controltovalidate = "ctl00_ContentPlaceHolder1_txtUnitName";
ctl00_ContentPlaceHolder1_rfvUnitName.focusOnError = "t";
ctl00_ContentPlaceHolder1_rfvUnitName.errormessage = "Required";
ctl00_ContentPlaceHolder1_rfvUnitName.display = "Dynamic";
ctl00_ContentPlaceHolder1_rfvUnitName.validationGroup = "UN";
ctl00_ContentPlaceHolder1_rfvUnitName.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_rfvUnitName.initialvalue = "";
var ctl00_ContentPlaceHolder1_rfvddlUnitName = document.all ? document.all["ctl00_ContentPlaceHolder1_rfvddlUnitName"] : document.getElementById("ctl00_ContentPlaceHolder1_rfvddlUnitName");
ctl00_ContentPlaceHolder1_rfvddlUnitName.controltovalidate = "ctl00_ContentPlaceHolder1_ddlUnitName";
ctl00_ContentPlaceHolder1_rfvddlUnitName.focusOnError = "t";
ctl00_ContentPlaceHolder1_rfvddlUnitName.errormessage = "Required";
ctl00_ContentPlaceHolder1_rfvddlUnitName.display = "Dynamic";
ctl00_ContentPlaceHolder1_rfvddlUnitName.validationGroup = "P";
ctl00_ContentPlaceHolder1_rfvddlUnitName.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_rfvddlUnitName.initialvalue = "0";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator20 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator20"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator20");
ctl00_ContentPlaceHolder1_RequiredFieldValidator20.controltovalidate = "ctl00_ContentPlaceHolder1_txtPFlat";
ctl00_ContentPlaceHolder1_RequiredFieldValidator20.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator20.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator20.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator20.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator20.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator20.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator21 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator21"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator21");
ctl00_ContentPlaceHolder1_RequiredFieldValidator21.controltovalidate = "ctl00_ContentPlaceHolder1_txtPBuilding";
ctl00_ContentPlaceHolder1_RequiredFieldValidator21.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator21.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator21.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator21.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator21.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator21.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator49 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator49"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator49");
ctl00_ContentPlaceHolder1_RequiredFieldValidator49.controltovalidate = "ctl00_ContentPlaceHolder1_txtPVillageTown";
ctl00_ContentPlaceHolder1_RequiredFieldValidator49.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator49.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator49.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator49.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator49.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator49.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator23 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator23"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator23");
ctl00_ContentPlaceHolder1_RequiredFieldValidator23.controltovalidate = "ctl00_ContentPlaceHolder1_txtPBlock";
ctl00_ContentPlaceHolder1_RequiredFieldValidator23.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator23.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator23.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator23.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator23.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator23.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator22 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator22"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator22");
ctl00_ContentPlaceHolder1_RequiredFieldValidator22.controltovalidate = "ctl00_ContentPlaceHolder1_txtPRoadStreetLane";
ctl00_ContentPlaceHolder1_RequiredFieldValidator22.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator22.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator22.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator22.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator22.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator22.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator25 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator25"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator25");
ctl00_ContentPlaceHolder1_RequiredFieldValidator25.controltovalidate = "ctl00_ContentPlaceHolder1_txtPCity";
ctl00_ContentPlaceHolder1_RequiredFieldValidator25.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator25.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator25.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator25.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator25.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator25.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator26 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator26"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator26");
ctl00_ContentPlaceHolder1_RequiredFieldValidator26.controltovalidate = "ctl00_ContentPlaceHolder1_txtPpin";
ctl00_ContentPlaceHolder1_RequiredFieldValidator26.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator26.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator26.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator26.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator26.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator26.initialvalue = "";
var ctl00_ContentPlaceHolder1_RegularExpressionValidator6 = document.all ? document.all["ctl00_ContentPlaceHolder1_RegularExpressionValidator6"] : document.getElementById("ctl00_ContentPlaceHolder1_RegularExpressionValidator6");
ctl00_ContentPlaceHolder1_RegularExpressionValidator6.controltovalidate = "ctl00_ContentPlaceHolder1_txtPpin";
ctl00_ContentPlaceHolder1_RegularExpressionValidator6.focusOnError = "t";
ctl00_ContentPlaceHolder1_RegularExpressionValidator6.errormessage = "Pin Code must be of 6 digit and not start with 0 and 9";
ctl00_ContentPlaceHolder1_RegularExpressionValidator6.display = "Dynamic";
ctl00_ContentPlaceHolder1_RegularExpressionValidator6.validationGroup = "P";
ctl00_ContentPlaceHolder1_RegularExpressionValidator6.evaluationfunction = "RegularExpressionValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RegularExpressionValidator6.validationexpression = "^([1-8])([0-9]){5}$";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator18 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator18"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator18");
ctl00_ContentPlaceHolder1_RequiredFieldValidator18.controltovalidate = "ctl00_ContentPlaceHolder1_ddlPState";
ctl00_ContentPlaceHolder1_RequiredFieldValidator18.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator18.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator18.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator18.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator18.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator18.initialvalue = "0";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator19 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator19"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator19");
ctl00_ContentPlaceHolder1_RequiredFieldValidator19.controltovalidate = "ctl00_ContentPlaceHolder1_ddlPDistrict";
ctl00_ContentPlaceHolder1_RequiredFieldValidator19.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator19.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator19.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator19.validationGroup = "P";
ctl00_ContentPlaceHolder1_RequiredFieldValidator19.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator19.initialvalue = "0";
var ctl00_ContentPlaceHolder1_CustomValidator2 = document.all ? document.all["ctl00_ContentPlaceHolder1_CustomValidator2"] : document.getElementById("ctl00_ContentPlaceHolder1_CustomValidator2");
ctl00_ContentPlaceHolder1_CustomValidator2.focusOnError = "t";
ctl00_ContentPlaceHolder1_CustomValidator2.errormessage = "Please enter Plant Address detail by click on Add Button";
ctl00_ContentPlaceHolder1_CustomValidator2.display = "Dynamic";
ctl00_ContentPlaceHolder1_CustomValidator2.validationGroup = "Y";
ctl00_ContentPlaceHolder1_CustomValidator2.evaluationfunction = "CustomValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_CustomValidator2.clientvalidationfunction = "CountGridRowPlant";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8");
ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8.controltovalidate = "ctl00_ContentPlaceHolder1_rdbCatgg";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator42 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator42"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator42");
ctl00_ContentPlaceHolder1_RequiredFieldValidator42.controltovalidate = "ctl00_ContentPlaceHolder1_rdbSubCategg";
ctl00_ContentPlaceHolder1_RequiredFieldValidator42.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator42.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator42.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator42.validationGroup = "Y";
ctl00_ContentPlaceHolder1_RequiredFieldValidator42.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator42.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator24 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator24"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator24");
ctl00_ContentPlaceHolder1_RequiredFieldValidator24.controltovalidate = "ctl00_ContentPlaceHolder1_txtsearchNic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator24.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator24.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator24.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator24.validationGroup = "C";
ctl00_ContentPlaceHolder1_RequiredFieldValidator24.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator24.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17");
ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17.controltovalidate = "ctl00_ContentPlaceHolder1_rdbCatggMultiple";
ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17.validationGroup = "B";
ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17.initialvalue = "";
var ctl00_ContentPlaceHolder1_RequiredFggieldValidator17 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFggieldValidator17"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFggieldValidator17");
ctl00_ContentPlaceHolder1_RequiredFggieldValidator17.controltovalidate = "ctl00_ContentPlaceHolder1_ddl2NicCode";
ctl00_ContentPlaceHolder1_RequiredFggieldValidator17.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFggieldValidator17.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFggieldValidator17.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFggieldValidator17.validationGroup = "B";
ctl00_ContentPlaceHolder1_RequiredFggieldValidator17.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFggieldValidator17.initialvalue = "0";
var ctl00_ContentPlaceHolder1_RequiredFieldddValidator17 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldddValidator17"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldddValidator17");
ctl00_ContentPlaceHolder1_RequiredFieldddValidator17.controltovalidate = "ctl00_ContentPlaceHolder1_ddl4NicCode";
ctl00_ContentPlaceHolder1_RequiredFieldddValidator17.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldddValidator17.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldddValidator17.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldddValidator17.validationGroup = "B";
ctl00_ContentPlaceHolder1_RequiredFieldddValidator17.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldddValidator17.initialvalue = "0";
var ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7 = document.all ? document.all["ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7"] : document.getElementById("ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7");
ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7.controltovalidate = "ctl00_ContentPlaceHolder1_ddl5NicCode";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7.focusOnError = "t";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7.errormessage = "Required";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7.display = "Dynamic";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7.validationGroup = "B";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7.evaluationfunction = "RequiredFieldValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7.initialvalue = "0";
var ctl00_ContentPlaceHolder1_cvRegProduct = document.all ? document.all["ctl00_ContentPlaceHolder1_cvRegProduct"] : document.getElementById("ctl00_ContentPlaceHolder1_cvRegProduct");
ctl00_ContentPlaceHolder1_cvRegProduct.focusOnError = "t";
ctl00_ContentPlaceHolder1_cvRegProduct.errormessage = "Please enter activity detail by click on Add Button";
ctl00_ContentPlaceHolder1_cvRegProduct.display = "Dynamic";
ctl00_ContentPlaceHolder1_cvRegProduct.validationGroup = "Y";
ctl00_ContentPlaceHolder1_cvRegProduct.evaluationfunction = "CustomValidatorEvaluateIsValid";
ctl00_ContentPlaceHolder1_cvRegProduct.clientvalidationfunction = "CountGridRowActivity";
//]]>
</script>


<script type="text/javascript" style="word-spacing: 0em;">
//<![CDATA[

var Page_ValidationActive = false;
if (typeof(ValidatorOnLoad) == "function") {
    ValidatorOnLoad();
}

function ValidatorOnSubmit() {
    if (Page_ValidationActive) {
        return ValidatorCommonOnSubmit();
    }
    else {
        return true;
    }
}
        
document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator3').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator3'));
}

document.getElementById('ctl00_ContentPlaceHolder1_rfvpannumber').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_rfvpannumber'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator5').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator5'));
}

document.getElementById('ctl00_ContentPlaceHolder1_rfvPreviousYearITR').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_rfvPreviousYearITR'));
}

document.getElementById('ctl00_ContentPlaceHolder1_rfvWhetherGstn').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_rfvWhetherGstn'));
}

document.getElementById('ctl00_ContentPlaceHolder1_cvGSTN').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_cvGSTN'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator47').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator47'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":2,"id":"ctl00_ContentPlaceHolder1_ftbMobile"}, null, null, $get("ctl00_ContentPlaceHolder1_txtmobile"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator14').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator14'));
}

document.getElementById('ctl00_ContentPlaceHolder1_revMobile').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_revMobile'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator15').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator15'));
}

document.getElementById('ctl00_ContentPlaceHolder1_revEmail').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_revEmail'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator16').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator16'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator8').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator8'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator17').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator17'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator1').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator1'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":15,"ValidChars":"a-zA-Z0-9 ()[]{}:;!+_-\\/?,.@#$%\u0026\u0027","id":"ctl00_ContentPlaceHolder1_fteEnterprise"}, null, null, $get("ctl00_ContentPlaceHolder1_txtenterprisename"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator30').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator30'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator31').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator31'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator32').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator32'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator33').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator33'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator36').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator36'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator34').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator34'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":2,"id":"ctl00_ContentPlaceHolder1_ftbPin"}, null, null, $get("ctl00_ContentPlaceHolder1_txtOffPin"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator4').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator4'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator1').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator1'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator5').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator5'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator6').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator6'));
}

document.getElementById('ctl00_ContentPlaceHolder1_reqlatude').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_reqlatude'));
}

document.getElementById('ctl00_ContentPlaceHolder1_reqlngt').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_reqlngt'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RFVPreviousNumber').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RFVPreviousNumber'));
}

document.getElementById('ctl00_ContentPlaceHolder1_CustomValidator4').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_CustomValidator4'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.CalendarBehavior, {"button":$get("ctl00_ContentPlaceHolder1_txtdateIncorporation"),"cssClass":"cal_Theme1","format":"dd/MM/yyyy","id":"ctl00_ContentPlaceHolder1_CalendarExtender1"}, null, null, $get("ctl00_ContentPlaceHolder1_txtdateIncorporation"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator37').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator37'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator38').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator38'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":13,"ValidChars":" ","id":"ctl00_ContentPlaceHolder1_FilteredTextBoxExtender9"}, null, null, $get("ctl00_ContentPlaceHolder1_txtBankName"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator35').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator35'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":14,"id":"ctl00_ContentPlaceHolder1_fltName"}, null, null, $get("ctl00_ContentPlaceHolder1_txtifsccode"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator13').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator13'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator4').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator4'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator11').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator11'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":2,"id":"ctl00_ContentPlaceHolder1_FilteredTextBoxExtender3"}, null, null, $get("ctl00_ContentPlaceHolder1_txtaccountno"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator3').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator3'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator39').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator39'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":2,"id":"ctl00_ContentPlaceHolder1_FilteredTextBoxExtender11"}, null, null, $get("ctl00_ContentPlaceHolder1_txtNoofpersonMale"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RangeValidator1').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RangeValidator1'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator40').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator40'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":2,"id":"ctl00_ContentPlaceHolder1_FilteredTextBoxExtender12"}, null, null, $get("ctl00_ContentPlaceHolder1_txtNoofpersonFemale"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RangeValidator2').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RangeValidator2'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator41').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator41'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":2,"id":"ctl00_ContentPlaceHolder1_FilteredTextBoxExtender13"}, null, null, $get("ctl00_ContentPlaceHolder1_txtNoofpersonOthers"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RangeValidator3').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RangeValidator3'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator9').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator9'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":2,"id":"ctl00_ContentPlaceHolder1_FilteredTextBoxExtender2"}, null, null, $get("ctl00_ContentPlaceHolder1_txttotalemp"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator2').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator2'));
}

document.getElementById('ctl00_ContentPlaceHolder1_Range1').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_Range1'));
}

document.getElementById('ctl00_ContentPlaceHolder1_CustomValidator1').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_CustomValidator1'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator44').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator44'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator46').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator46'));
}

document.getElementById('ctl00_ContentPlaceHolder1_rfvNCS').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_rfvNCS'));
}

document.getElementById('ctl00_ContentPlaceHolder1_rfvNSIC').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_rfvNSIC'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator12').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator12'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator312').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator312'));
}

document.getElementById('ctl00_ContentPlaceHolder1_rfvUnitName').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_rfvUnitName'));
}

document.getElementById('ctl00_ContentPlaceHolder1_rfvddlUnitName').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_rfvddlUnitName'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator20').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator20'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator21').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator21'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator49').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator49'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator23').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator23'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator22').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator22'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator25').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator25'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator26').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator26'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.FilteredTextBoxBehavior, {"FilterType":2,"id":"ctl00_ContentPlaceHolder1_FilteredTextBoxExtender5"}, null, null, $get("ctl00_ContentPlaceHolder1_txtPpin"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator6').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RegularExpressionValidator6'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator18').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator18'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator19').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator19'));
}

document.getElementById('ctl00_ContentPlaceHolder1_CustomValidator2').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_CustomValidator2'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator1s8'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator42').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator42'));
}
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.AutoCompleteBehavior, {"completionInterval":100,"completionListCssClass":"AutoExtender","completionListElementID":"listPlacement","completionListItemCssClass":"AutoExtenderList","delimiterCharacters":";, :","highlightedItemCssClass":"AutoExtenderHighlight","id":"ctl00_ContentPlaceHolder1_ajaxAutoCompExted","minimumPrefixLength":1,"serviceMethod":"GetCompletionList","servicePath":"/Udyam_User/Udyam_UpdateNew.aspx","useContextKey":true}, null, null, $get("ctl00_ContentPlaceHolder1_txtsearchNic"));
});

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator24').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator24'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidatorggg17'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFggieldValidator17').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFggieldValidator17'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldddValidator17').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldddValidator17'));
}

document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_RequiredFieldValidator1e7'));
}

document.getElementById('ctl00_ContentPlaceHolder1_cvRegProduct').dispose = function() {
    Array.remove(Page_Validators, document.getElementById('ctl00_ContentPlaceHolder1_cvRegProduct'));
}
Sys.Application.add_init(function() {
    $create(Sys.UI._UpdateProgress, {"associatedUpdatePanelId":"ctl00_ContentPlaceHolder1_UpdatePaneldd1","displayAfter":500,"dynamicLayout":true}, null, null, $get("ctl00_ContentPlaceHolder1_UpdateProgress4"));
});
//]]>
</script>
<input type="hidden" value="1" id="hiddenInputToUpdateATBuffer_CommonToolkitScripts" name="hiddenInputToUpdateATBuffer_CommonToolkitScripts" style="word-spacing: 0em;"></form>
    <!-- ======= Footer ======= -->
  <footer id="footer" style="zoom: 1; word-spacing: 0em;">
   

    <div class="footer-top" style="word-spacing: 0em;">
      <div class="container" style="word-spacing: 0em;">
        <div class="row" style="word-spacing: 0em;">

          <div class="col-lg-4 col-md-6 footer-contact" style="word-spacing: 0em;">
            <h3 style="word-spacing: 0em;">Udyam Registration</h3>
            <p style="word-spacing: 0em;">
              Ministry of MSME <br style="word-spacing: 0em;">
             Udyog bhawan - New Delhi<br style="word-spacing: 0em;">
               <br style="word-spacing: 0em;">
             
              <strong style="word-spacing: 0em;">Email:</strong> <EMAIL><br style="word-spacing: 0em;">
                  <br style="word-spacing: 0em;">
               <strong style="word-spacing: 0em;"> <a style="color: rgb(255, 255, 255); word-spacing: 0em;" href="/ContactUs.aspx" aria-label="Contact Us">Contact Us</a></strong>
                <br style="word-spacing: 0em;">
                 <strong style="word-spacing: 0em;"> <a style="color: rgb(255, 255, 255); word-spacing: 0em;" href="https://champions.gov.in/" target="_blank" aria-label="For Grievances / Problems">For Grievances / Problems</a></strong>
            </p>
          </div>
             <div class="col-lg-4 col-md-6 footer-links" style="word-spacing: 0em;">
            <h4 style="word-spacing: 0em;">Our Services</h4>
            <ul style="word-spacing: 0em;">
              <li style="word-spacing: 0em;"><i class="bx bx-chevron-right" style="word-spacing: 0em;"></i> <a href="https://champions.gov.in/" aria-label="Champions" style="word-spacing: 0em;">CHAMPIONS</a></li>
              <li style="word-spacing: 0em;"><i class="bx bx-chevron-right" style="word-spacing: 0em;"></i> <a href="https://samadhaan.msme.gov.in/" aria-label="MSME Samadhaan" style="word-spacing: 0em;">MSME Samadhaan</a></li>
              <li style="word-spacing: 0em;"><i class="bx bx-chevron-right" style="word-spacing: 0em;"></i> <a href="https://sambandh.msme.gov.in/" aria-label="MSME Sambandh" style="word-spacing: 0em;">MSME Sambandh</a></li>
             
                 <li style="word-spacing: 0em;"><i class="bx bx-chevron-right" style="word-spacing: 0em;"></i> <a href="https://dashboard.msme.gov.in/" aria-label="MSME Dashboard" style="word-spacing: 0em;">MSME Dashboard</a></li>
              <li style="word-spacing: 0em;"><i class="bx bx-chevron-right" style="word-spacing: 0em;"></i> <a href="https://msmedi.dcmsme.gov.in/" aria-label="ESDP" style="word-spacing: 0em;">Entrepreneurship Skill Development Programme (ESDP)</a></li>
            </ul>
          </div>
         
            
          
               <div class="col-lg-4 col-md-6 footer-newsletter" style="word-spacing: 0em;">
                   <h4 style="word-spacing: 0em;">Video</h4>
                   <video controls="controls" poster="/videos/udyam.png" width="100%" style="word-spacing: 0em;">  
                                                                                                                           
                                                <source src="/videos/udyam.mp4" type="video/mp4" style="word-spacing: 0em;">
                                                 
                                            </video>
           
              
          
          </div>
         

       

        </div>
      </div>
    </div>

    <div class="container" style="word-spacing: 0em;">

      <div class="copyright-wrap d-md-flex py-4" style="word-spacing: 0em;">
        <div class="mr-md-auto text-center text-md-left" style="word-spacing: 0em;">
         <div class="copyright" style="word-spacing: 0em;">

           © Copyright <strong style="word-spacing: 0em;"><span style="word-spacing: 0em;">Udyam Registration</span></strong>.  All Rights Reserved,  Website Content Managed by Ministry of Micro Small and Medium Enterprises, GoI 
         
          </div>
          <div class="credits" style="word-spacing: 0em;">         
           Website hosted &amp; managed by  
                        <span class="1" style="word-spacing: 0em;"></span><a style="color: rgb(255, 255, 255); word-spacing: 0em;" href="http://home.nic.in/" target="_blank" aria-label="National Informatics Centre">National Informatics Centre</a>, <a style="color: rgb(255, 255, 255); word-spacing: 0em;" href="http://deity.gov.in/" target="_blank" aria-label="Ministry of Communication and IT">Ministry of Communications and IT</a>, <a style="color: rgb(255, 255, 255); word-spacing: 0em;" href="http://india.gov.in/" target="_blank">Government of India</a>
          </div>
        </div>
       
          <div class="social-links text-center text-md-right pt-3 pt-md-0" style="word-spacing: 0em;">
          <a href="https://twitter.com/minmsme?original_referer=http%3A%2F%2Fmsme.gov.in%2FWeb%2FPortal%2FSocialMedia.aspx&amp;profile_id=2595957175&amp;tw_i=539287566780874752&amp;tw_p=embeddedtimeline&amp;tw_w=483558219873144833" class="twitter" onclick="return confirm('You will be transferred to an external web site not controlled by MoMSME. MoMSME is not responsible for any of the info or services provided by this web site. \nAre you sure you want to proceed?')" aria-label="Follow us on Twitter" style="word-spacing: 0em;"><svg height="1em" viewBox="0 0 300 300" version="1.1" xmlns="http://www.w3.org/2000/svg" style="word-spacing: 0em;">
                                <path d="M178.57 127.15 290.27 0h-26.46l-97.03 110.38L89.34 0H0l117.13 166.93L0 300.25h26.46l102.4-116.59 81.8 116.59h89.34M36.01 19.54H76.66l187.13 262.13h-40.66" fill="white" stroke="white" style="word-spacing: 0em;"></path>
                            </svg></a>
          <a href="https://www.facebook.com/minmsme" class="facebook" aria-label="Follow us on Facebook" onclick="return confirm('You will be transferred to an external web site not controlled by MoMSME. MoMSME is not responsible for any of the info or services provided by this web site. \nAre you sure you want to proceed?')" style="word-spacing: 0em;"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512" style="word-spacing: 0em;"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z" fill="white" stroke="white" style="word-spacing: 0em;"></path></svg></a>
          <a href="https://www.instagram.com/minmsme/" class="instagram" aria-label="Follow us on Instagram" onclick="return confirm('You will be transferred to an external web site not controlled by MoMSME. MoMSME is not responsible for any of the info or services provided by this web site. \nAre you sure you want to proceed?')" style="word-spacing: 0em;"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512" style="word-spacing: 0em;"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" fill="white" stroke="white" style="word-spacing: 0em;"></path></svg></a>
         
        </div>
      </div>

    </div>
  </footer><!-- End Footer -->

  <a href="#" class="back-to-top" style="margin-bottom: 5%; zoom: 1; word-spacing: 0em; display: inline;"><i class="icofont-simple-up" style="word-spacing: 0em;"></i></a>
  
     <!-- Vendor JS Files -->
  <script src="../assets/vendor/jquery/jquery.min.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/jquery.easing/jquery.easing.min.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/php-email-form/validate.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/waypoints/jquery.waypoints.min.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/counterup/counterup.min.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/owl.carousel/owl.carousel.min.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/venobox/venobox.min.js" style="zoom: 1; word-spacing: 0em;"></script>
  <script src="../assets/vendor/aos/aos.js" style="zoom: 1; word-spacing: 0em;"></script>

  <!-- Template Main JS File -->
  <script src="../assets/js/main.js" style="zoom: 1; word-spacing: 0em;"></script><nav class="mobile-nav d-lg-none" style="zoom: 1; word-spacing: 0em;">
                <ul style="word-spacing: 0em;">
                    <li class="active" style="word-spacing: 0em;"><a href="udyam_Dashboard.aspx" style="word-spacing: 0em;">Home</a></li>
                    
                    <li style="word-spacing: 0em;">
                        
                    </li>
                    <li style="word-spacing: 0em;"><a href="Udyam_Investment_TurnoverDetail.aspx" style="word-spacing: 0em;">View Classification Details</a></li>
                   
                    <li style="word-spacing: 0em;"><a href="Udyam_Cancellation.aspx" style="word-spacing: 0em;">Cancellation</a></li>
                    
                    <li style="word-spacing: 0em;"><a href="../Udyam_Logout.aspx?type=udyam" style="word-spacing: 0em;">Logout</a></li>
                </ul>
            </nav><div class="mobile-nav-overly" style="zoom: 1; word-spacing: 0em;"></div>

         <script src="https://cdn.ux4g.gov.in/accessibility-beta-v1.14/accessibility-widget.js" style="zoom: 1; word-spacing: 0em;"></script>



  <meta charset="UTF-8" style="zoom: 1; word-spacing: 0em;">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" style="zoom: 1; word-spacing: 0em;">
  <title style="zoom: 1; word-spacing: 0em;">UX4G Accessibility Tool</title>
<link rel="stylesheet" href="https://cdn.ux4g.gov.in/accessibility-beta-v1.15/accessibility-widget.css" style="zoom: 1; word-spacing: 0em;">




<div class="uwaw uw-light-theme gradient-head uwaw-initial paid_widget" id="uw-main" role="dialog" aria-modal="true" aria-labelledby="uw-heading">
    <div class="relative second-panel">
        <h2 id="uw-heading">Accessibility options</h2>
        <button type="button" aria-label="Close main navigation panel" class="uwaw-close" tabindex="2"></button>
    </div>
    <div class="uwaw-body">
        <div class="h-scroll">
            <div class="uwaw-features">
                <div class="uwaw-features__item reset-feature" id="featureItem_sp">
                    <button aria-label="Text To Speech" tabindex="3" id="speak" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-speaker" role="img" aria-label="Text to speech icon" aria-hidden="true" aria-pressed="false"></span>
                        </span>
                        <span class="uwaw-features__item__name">Text To Speech</span>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon_sp" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <div class="uwaw-features__item reset-feature" id="featureItem">
                    <button aria-label="Bigger Text" tabindex="4" id="btn-s9" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-bigger-text" role="img" aria-label="Bigger text icon" aria-hidden="true" aria-pressed="false"></span>
                        </span>
                        <span class="uwaw-features__item__name">Bigger Text</span>
                        <div class="uwaw-features__item__steps reset-steps" id="featureSteps">
                            <span class="step uwaw-features__step"></span>
                            <span class="step uwaw-features__step"></span>
                            <span class="step uwaw-features__step"></span>
                            <span class="step uwaw-features__step"></span>
                        </div>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <div class="uwaw-features__item reset-feature" id="featureItem-ts">
                    <button aria-label="Text Spacing" tabindex="5" id="btn-s13" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-text-spacing" role="img" aria-label="Text spacing icon" aria-hidden="true" aria-pressed="false"></span>
                        </span>
                        <span class="uwaw-features__item__name">Text Spacing</span>
                        <div class="uwaw-features__item__steps reset-steps" id="featureSteps-ts">
                            <span class="step uwaw-features__step"></span>
                            <span class="step uwaw-features__step"></span>
                            <span class="step uwaw-features__step"></span>
                        </div>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon-ts" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <div class="uwaw-features__item reset-feature" id="featureItem-lh">
                    <button aria-label="Line Height" tabindex="6" id="btn-s12" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-line-hight" role="img" aria-label="Line height icon" aria-hidden="true" aria-pressed="false"></span>
                        </span>
                        <span class="uwaw-features__item__name">Line Height</span>
                        <div class="uwaw-features__item__steps reset-steps" id="featureSteps-lh">
                            <span class="step uwaw-features__step"></span>
                            <span class="step uwaw-features__step"></span>
                            <span class="step uwaw-features__step"></span>
                            <span class="step uwaw-features__step"></span>
                        </div>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon-lh" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <div class="uwaw-features__item reset-feature" id="featureItem-ht">
                    <button aria-pressed="false" aria-label="Highlight Links" tabindex="7" id="btn-s10" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-highlight-links" role="img" aria-label="Highlight links icon" aria-hidden="true"></span>
                        </span>
                        <span class="uwaw-features__item__name">Highlight Links</span>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon-ht" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <div class="uwaw-features__item reset-feature" id="featureItem-df">
                    <button aria-label="Dyslexia Friendly Font" aria-pressed="false" tabindex="8" id="btn-df" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-dyslexia-font" role="img" aria-label="Dyslexia friendly font icon" aria-hidden="true"></span>
                        </span>
                        <span class="uwaw-features__item__name">Dyslexia Friendly</span>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon-df" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <div class="uwaw-features__item reset-feature" id="featureItem-hi">
                    <button aria-label="Hide Images" aria-pressed="false" tabindex="9" id="btn-s11" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-hide-images" role="img" aria-label="Hide images icon" aria-hidden="true"></span>
                        </span>
                        <span class="uwaw-features__item__name">Hide Images</span>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon-hi" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <div class="uwaw-features__item reset-feature" id="featureItem-Cursor">
                    <button aria-label="Cursor Bigger" aria-pressed="false" tabindex="10" id="btn-cursor" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-cursor" role="img" aria-label="Cursor bigger icon" aria-hidden="true"></span>
                        </span>
                        <span class="uwaw-features__item__name">Cursor</span>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon-cursor" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <div class="uwaw-features__item reset-feature" id="featureItem-ht-dark">
                    <button aria-label="Light Dark Theme" aria-pressed="false" tabindex="11" id="dark-btn" class="uwaw-features__item__i">
                        <div class="uwaw-features__item__name">
                            <div class="light_dark_icon">
                                <input type="checkbox" class="light_mode uwaw-featugres__item__i" id="checkbox" aria-label="Toggle light and dark mode" role="switch">
                                <label for="checkbox" class="checkbox-label">
                                    <i class="fas fa-moon-stars">
                                        <span class="ux4g-icon icon-moon" role="img" aria-label="Dark mode icon" aria-hidden="true"></span>
                                    </i>
                                    <i class="fas fa-sun">
                                        <span class="ux4g-icon icon-sun" role="img" aria-label="Light mode icon" aria-hidden="true"></span>
                                    </i>
                                    <span class="ball"></span>
                                </label>
                            </div>
                            <span class="uwaw-features__item__name">Light-Dark</span>
                        </div>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon-ht-dark" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
                <!-- Invert Colors Widget -->
                <div class="uwaw-features__item reset-feature" id="featureItem-ic">
                    <button aria-label="Invert Colors" aria-pressed="false" tabindex="12" id="btn-invert" class="uwaw-features__item__i">
                        <span class="uwaw-features__item__icon">
                            <span class="ux4g-icon icon-invert" role="img" aria-label="Invert colors icon" aria-hidden="true"></span>
                        </span>
                        <span class="uwaw-features__item__name">Invert Colors</span>
                        <span class="tick-active uwaw-features__item__enabled reset-tick" id="tickIcon-ic" aria-live="polite" role="status" style="display: none;"></span>
                    </button>
                </div>
            </div>
        </div>
        <!-- Reset Button -->
    </div>
    <div class="reset-panel">
        <!-- copyright accessibility bar -->
        <div class="copyrights-accessibility">
            <button aria-label="Reset All Settings" tabindex="13" class="btn-reset-all" id="reset-all">
                <div class="reset-icon"></div>
                <div class="reset-btn-text">Reset All Settings</div>
            </button>
            <a tabindex="-1" href="https://www.ux4g.gov.in" target="_blank" class="copyright-text" contenteditable="false" style="cursor: pointer;">
                <span class="uwaw-features__item__name ux4g-copy ux4g-copyright">Created by</span>
                <img src="https://www.ux4g.gov.in/assets/img/logo/ux4g-logo.svg" alt="UX4G Logo" loading="lazy">
            </a>
        </div>
    </div>
</div>
<button tabindex="1" aria-label="Accessibility Options" data-uw-trigger="true" aria-haspopup="dialog" aria-controls="uw-main" aria-expanded="false" id="uw-widget-custom-trigger" class="uw-widget-custom-trigger" style="zoom: 1; word-spacing: 0em;">
    <img alt="icon" loading="lazy" src="data:image/svg+xml,%0A%3Csvg width='32' height='32' viewBox='0 0 32 32' fill='none'
		xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_1_1506)'%3E%3Cpath d='M16 7C15.3078 7 14.6311 6.79473 14.0555 6.41015C13.4799 6.02556 13.0313 5.47894 12.7664 4.83939C12.5015 4.19985 12.4322 3.49612 12.5673 2.81719C12.7023 2.13825 13.0356 1.51461 13.5251 1.02513C14.0146 0.535644 14.6383 0.202301 15.3172 0.0672531C15.9961 -0.0677952 16.6999 0.00151652 17.3394 0.266423C17.9789 0.53133 18.5256 0.979934 18.9101 1.55551C19.2947 2.13108 19.5 2.80777 19.5 3.5C19.499 4.42796 19.1299 5.31762 18.4738 5.97378C17.8176 6.62994 16.928 6.99901 16 7Z' fill='white'/%3E%3Cpath d='M27 7.05L26.9719 7.0575L26.9456 7.06563C26.8831 7.08313 26.8206 7.10188 26.7581 7.12125C25.595 7.4625 19.95 9.05375 15.9731 9.05375C12.2775 9.05375 7.14313 7.67875 5.50063 7.21188C5.33716 7.14867 5.17022 7.09483 5.00063 7.05063C3.81313 6.73813 3.00063 7.94438 3.00063 9.04688C3.00063 10.1388 3.98188 10.6588 4.9725 11.0319V11.0494L10.9238 12.9081C11.5319 13.1413 11.6944 13.3794 11.7738 13.5856C12.0319 14.2475 11.8256 15.5581 11.7525 16.0156L11.39 18.8281L9.37813 29.84C9.37188 29.87 9.36625 29.9006 9.36125 29.9319L9.34688 30.0112C9.20188 31.0206 9.94313 32 11.3469 32C12.5719 32 13.1125 31.1544 13.3469 30.0037C13.5813 28.8531 15.0969 20.1556 15.9719 20.1556C16.8469 20.1556 18.6494 30.0037 18.6494 30.0037C18.8838 31.1544 19.4244 32 20.6494 32C22.0569 32 22.7981 31.0162 22.6494 30.0037C22.6363 29.9175 22.6206 29.8325 22.6019 29.75L20.5625 18.8294L20.2006 16.0169C19.9387 14.3788 20.1494 13.8375 20.2206 13.7106C20.2225 13.7076 20.2242 13.7045 20.2256 13.7013C20.2931 13.5763 20.6006 13.2963 21.3181 13.0269L26.8981 11.0763C26.9324 11.0671 26.9662 11.0563 26.9994 11.0438C27.9994 10.6688 28.9994 10.15 28.9994 9.04813C28.9994 7.94625 28.1875 6.73813 27 7.05Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_1_1506'%3E%3Crect width='32' height='32' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A" style="word-spacing: 0em;">
    <span style="word-spacing: 0em;">Accessibility Options</span>
</button>



</body></html>